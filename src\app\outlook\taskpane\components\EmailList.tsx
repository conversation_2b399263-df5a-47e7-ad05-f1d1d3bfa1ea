import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';
import { Mail, X, Calendar, User, Send } from 'lucide-react';
import type { FullEmailInfo } from '../stores/EmailStore';

interface EmailListProps {
  selectedEmails: FullEmailInfo[];
  onRemoveEmail: (emailId: string) => void;
  onDistribute?: () => Promise<void>;
}

export function EmailList({
  selectedEmails,
  onRemoveEmail,
  onDistribute
}: EmailListProps) {
  if (selectedEmails.length === 0) {
    return null;
  }

  const formatDate = (dateString: string | undefined) => {
    if (!dateString) return '未知时间';
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const truncateText = (text: string, maxLength: number = 50) => {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
  };

  return (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-sm">
          <Mail className="h-4 w-4" />
          选中的邮件列表 ({selectedEmails.length})
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        <div className="max-h-200 overflow-y-auto space-y-2">
          {selectedEmails.map((email, index) => (
            <div
              key={email.itemId || index}
              className="border rounded-lg p-3 bg-muted/30 hover:bg-muted/50 transition-colors"
            >
              <div className="flex items-start justify-between gap-2">
                <div className="flex-1 min-w-0">
                  {/* 邮件主题 */}
                  <div className="font-medium text-sm mb-1">
                    {truncateText(email.subject || '无主题', 40)}
                  </div>
                  
                  {/* 发件人 */}
                  <div className="flex items-center gap-1 text-xs mb-1">
                    <User className="h-3 w-3" />
                    <span>{email.sender || '未知发件人'}</span>
                  </div>

                  {/* 接收时间 */}
                  <div className="flex items-center gap-1 text-xs ">
                    <Calendar className="h-3 w-3" />
                    <span>{formatDate(email.receivedTime)}</span>
                  </div>
                </div>
                
                {/* 删除按钮 */}
                <Button
                  onClick={() => onRemoveEmail(email.itemId || '')}
                  variant="warning"
                  size="sm"
                  className="h-6 w-6 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
                >
                  <X className="h-3 w-3" />
                </Button>
              </div>
              
              {/* 邮件ID标识 */}
              <div className="mt-2">
                <Badge variant="info" className="text-xs">
                  ID: {email.itemId ? email.itemId.substring(0, 8) + '...' : 'N/A'}
                </Badge>
              </div>
            </div>
          ))}
        </div>

        {/* 批量分发按钮 */}
        {onDistribute && selectedEmails.length > 0 && (
          <div className="pt-3 border-t">
            <Button
              onClick={onDistribute}
              className="w-full"
              size="sm"
            >
              <Send className="h-4 w-4 mr-2" />
              创建任务 ({selectedEmails.length} 封邮件)
            </Button>
          </div>
        )}

      </CardContent>
    </Card>
  );
}
