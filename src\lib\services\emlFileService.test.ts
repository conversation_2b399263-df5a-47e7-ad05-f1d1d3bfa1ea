import { EmlFileService, EmailDetailsForEml } from './emlFileService'
import { promises as fs } from 'fs'
import path from 'path'

describe('EmlFileService', () => {
  let emlFileService: EmlFileService
  const testUploadDir = path.join(process.cwd(), 'test-uploads')

  beforeEach(() => {
    emlFileService = new EmlFileService()
  })

  afterEach(async () => {
    // 清理测试文件
    try {
      await fs.rmdir(testUploadDir, { recursive: true })
    } catch (error) {
      // 忽略清理错误
    }
  })

  test('应该成功生成EML文件', async () => {
    const emailDetails: EmailDetailsForEml = {
      from: '<EMAIL>',
      to: '<EMAIL>',
      cc: '<EMAIL>',
      subject: '测试邮件主题',
      date: new Date().toUTCString(),
      body: '<html><body><h1>测试邮件内容</h1><p>这是一封测试邮件。</p></body></html>',
      attachments: [
        {
          name: 'test.txt',
          size: 100,
          contentType: 'text/plain',
          content: Buffer.from('测试附件内容').toString('base64'),
          isInline: false,
          id: 'att1',
          encoding: 'base64'
        }
      ]
    }

    const mailId = 'test-mail-id-123'
    const result = await emlFileService.generateEmlFromEmailDetails(emailDetails, mailId)

    // 验证返回结果
    expect(result).toHaveProperty('filePath')
    expect(result).toHaveProperty('fileName')
    expect(result).toHaveProperty('fileSize')
    expect(result).toHaveProperty('fileHash')
    expect(result.fileName).toContain('.eml')
    expect(result.fileSize).toBeGreaterThan(0)

    // 验证文件是否存在 (使用服务的方法来读取，因为现在返回的是相对路径)
    const fileContent = await emlFileService.readEmlFile(result.filePath)
    expect(fileContent).toContain('From: <EMAIL>')
    expect(fileContent).toContain('To: <EMAIL>')
    expect(fileContent).toContain('Cc: <EMAIL>')
    expect(fileContent).toContain('Subject: 测试邮件主题')
    expect(fileContent).toContain('测试邮件内容')
    expect(fileContent).toContain('Content-Type: text/plain')
    expect(fileContent).toContain('filename="test.txt"')

    // 清理测试文件
    await emlFileService.deleteEmlFile(result.filePath)
  })

  test('应该正确处理特殊字符的邮件主题', async () => {
    const emailDetails: EmailDetailsForEml = {
      from: '<EMAIL>',
      to: '<EMAIL>',
      cc: '',
      subject: '包含特殊字符的主题: <test> "quotes" /slash\\backslash',
      date: new Date().toUTCString(),
      body: '<html><body>测试内容</body></html>',
      attachments: []
    }

    const mailId = 'test-special-chars'
    const result = await emlFileService.generateEmlFromEmailDetails(emailDetails, mailId)

    // 验证文件名不包含特殊字符
    expect(result.fileName).not.toMatch(/[<>:"/\\|?*]/)
    expect(result.fileName).toContain('.eml')

    // 清理测试文件
    await emlFileService.deleteEmlFile(result.filePath)
  })

  test('应该正确处理没有附件的邮件', async () => {
    const emailDetails: EmailDetailsForEml = {
      from: '<EMAIL>',
      to: '<EMAIL>',
      cc: '',
      subject: '无附件邮件',
      date: new Date().toUTCString(),
      body: '<html><body>这是一封没有附件的邮件</body></html>',
      attachments: []
    }

    const mailId = 'no-attachments'
    const result = await emlFileService.generateEmlFromEmailDetails(emailDetails, mailId)

    expect(result.fileSize).toBeGreaterThan(0)

    // 验证文件内容
    const fileContent = await emlFileService.readEmlFile(result.filePath)
    expect(fileContent).toContain('这是一封没有附件的邮件')
    expect(fileContent).not.toContain('Content-Disposition: attachment')

    // 清理测试文件
    await emlFileService.deleteEmlFile(result.filePath)
  })

  test('应该正确读取EML文件内容', async () => {
    const emailDetails: EmailDetailsForEml = {
      from: '<EMAIL>',
      to: '<EMAIL>',
      cc: '',
      subject: '读取测试',
      date: new Date().toUTCString(),
      body: '<html><body>读取测试内容</body></html>',
      attachments: []
    }

    const mailId = 'read-test'
    const result = await emlFileService.generateEmlFromEmailDetails(emailDetails, mailId)

    // 读取文件内容
    const content = await emlFileService.readEmlFile(result.filePath)
    expect(content).toContain('读取测试内容')
    expect(content).toContain('From: <EMAIL>')

    // 获取文件信息
    const fileInfo = await emlFileService.getEmlFileInfo(result.filePath)
    expect(fileInfo).not.toBeNull()
    expect(fileInfo?.size).toBe(result.fileSize)
    expect(fileInfo?.hash).toBe(result.fileHash)

    // 清理测试文件
    await emlFileService.deleteEmlFile(result.filePath)
  })

  test('应该能够解析生成的EML文件并保持数据一致性', async () => {
    const originalEmailDetails: EmailDetailsForEml = {
      from: '<EMAIL>',
      to: '<EMAIL>',
      cc: '<EMAIL>',
      subject: '测试解析EML文件',
      date: new Date('2024-01-15T10:30:00Z').toUTCString(),
      body: '<html><body><h1>测试内容</h1><p>这是一封用于测试解析的邮件。</p></body></html>',
      attachments: [
        {
          name: 'test-doc.txt',
          size: 50,
          contentType: 'text/plain',
          content: Buffer.from('这是测试文档内容').toString('base64'),
          isInline: false,
          id: 'test-doc',
          encoding: 'base64'
        },
        {
          name: 'inline-image.png',
          size: 100,
          contentType: 'image/png',
          content: Buffer.from('fake-image-data').toString('base64'),
          isInline: true,
          id: 'inline-img',
          encoding: 'base64'
        }
      ]
    }

    const mailId = 'parse-test'

    // 1. 生成EML文件
    const result = await emlFileService.generateEmlFromEmailDetails(originalEmailDetails, mailId)

    // 2. 解析EML文件
    const parsedEmailDetails = await emlFileService.parseEmlFile(result.filePath)

    // 3. 验证基本信息
    expect(parsedEmailDetails.from).toBe(originalEmailDetails.from)
    expect(parsedEmailDetails.to).toBe(originalEmailDetails.to)
    expect(parsedEmailDetails.cc).toBe(originalEmailDetails.cc)
    expect(parsedEmailDetails.subject).toBe(originalEmailDetails.subject)
    expect(parsedEmailDetails.date).toBe(originalEmailDetails.date)

    // 4. 验证邮件正文
    expect(parsedEmailDetails.body).toContain('测试内容')
    expect(parsedEmailDetails.body).toContain('这是一封用于测试解析的邮件')

    // 5. 验证附件数量
    expect(parsedEmailDetails.attachments).toHaveLength(2)

    // 6. 验证附件信息
    const textAttachment = parsedEmailDetails.attachments.find(att => att.name === 'test-doc.txt')
    expect(textAttachment).toBeDefined()
    expect(textAttachment?.contentType).toBe('text/plain')
    expect(textAttachment?.isInline).toBe(false)
    expect(textAttachment?.encoding).toBe('base64')

    const imageAttachment = parsedEmailDetails.attachments.find(att => att.name === 'inline-image.png')
    expect(imageAttachment).toBeDefined()
    expect(imageAttachment?.contentType).toBe('image/png')
    expect(imageAttachment?.isInline).toBe(true)
    expect(imageAttachment?.encoding).toBe('base64')

    // 清理测试文件
    await emlFileService.deleteEmlFile(result.filePath)
  })

  test('应该能够解析没有附件的EML文件', async () => {
    const originalEmailDetails: EmailDetailsForEml = {
      from: '<EMAIL>',
      to: '<EMAIL>',
      cc: '',
      subject: '简单邮件测试',
      date: new Date().toUTCString(),
      body: '<html><body><p>这是一封简单的邮件，没有附件。</p></body></html>',
      attachments: []
    }

    const mailId = 'simple-parse-test'

    // 生成并解析EML文件
    const result = await emlFileService.generateEmlFromEmailDetails(originalEmailDetails, mailId)
    const parsedEmailDetails = await emlFileService.parseEmlFile(result.filePath)

    // 验证基本信息
    expect(parsedEmailDetails.from).toBe(originalEmailDetails.from)
    expect(parsedEmailDetails.to).toBe(originalEmailDetails.to)
    expect(parsedEmailDetails.subject).toBe(originalEmailDetails.subject)
    expect(parsedEmailDetails.attachments).toHaveLength(0)
    expect(parsedEmailDetails.body).toContain('这是一封简单的邮件')

    // 清理测试文件
    await emlFileService.deleteEmlFile(result.filePath)
  })
})
