'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/Button'
import { Table, TableHeader, TableBody, TableRow, TableHead, TableCell } from '@/components/ui/Table'
import { TemplateTypeConfigForm } from '@/components/forms/TemplateTypeConfigForm'
import { useEmailTemplateTypeConfigs } from '@/lib/hooks/useEmailTemplateTypeConfigs'

interface EmailTemplateTypeConfig {
  id: number
  templateType: string
  projectId?: number
  defaultRecipients?: string[]
  defaultCcRecipients?: string[]
  description?: string
  createdAt: string
  updatedAt: string
  project?: {
    id: number
    projectCode: string
    projectName: string
  }
}

interface Project {
  id: number
  projectCode: string
  projectName: string
}

const templateTypeMap = {
  'CONFIRMATION': '确认回复',
  'SAFETY_NOTIFICATION': '安全性通知',
  'INTERNAL_FORWARD': '内部转发',
  'WM_FORWARD': 'WM转发'
}

export default function TemplateConfigsPage() {
  const [projects, setProjects] = useState<Project[]>([])
  const [selectedProjectId, setSelectedProjectId] = useState<string>('')
  const [showForm, setShowForm] = useState(false)
  const [editingConfig, setEditingConfig] = useState<EmailTemplateTypeConfig | null>(null)
  const [formMode, setFormMode] = useState<'create' | 'edit'>('create')

  // 使用自定义hook管理配置
  const {
    configs,
    isLoading,
    error,
    fetchConfigs,
    createConfig,
    updateConfig,
    deleteConfig, 
  } = useEmailTemplateTypeConfigs({
    projectId: selectedProjectId || undefined,
    autoFetch: true
  })

  // 获取项目列表
  const fetchProjects = async () => {
    try {
      const response = await fetch('/api/projects')
      if (response.ok) {
        const result = await response.json()
        if (result.success) {
          setProjects(result.data.projects)
        }
      }
    } catch (error) {
      console.error('Failed to fetch projects:', error)
    }
  }

  useEffect(() => {
    fetchProjects()
  }, [])

  // 当选择的项目改变时，重新获取配置
  useEffect(() => {
    fetchConfigs()
  }, [selectedProjectId, fetchConfigs])

  // 处理新建配置
  const handleCreate = () => {
    setEditingConfig(null)
    setFormMode('create')
    setShowForm(true)
  }

  // 处理编辑配置
  const handleEdit = (config: EmailTemplateTypeConfig) => {
    setEditingConfig(config)
    setFormMode('edit')
    setShowForm(true)
  }

  // 处理删除配置
  const handleDelete = async (id: number) => {
    if (!confirm('确定要删除这个配置吗？')) {
      return
    }

    try {
      await deleteConfig(id)
    } catch (error) {
      alert(error instanceof Error ? error.message : '删除失败')
    }
  }

  // 处理表单提交
  const handleSubmit = async (configData: Partial<EmailTemplateTypeConfig>) => {
    try {
      if (formMode === 'create') {
        await createConfig(configData)
      } else {
        await updateConfig(configData)
      }
      setShowForm(false)
    } catch (error) {
      console.error('Submit failed:', error)
      throw error
    }
  }
 

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-gray-500">加载中...</div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-red-500">加载失败: {error}</div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">邮件模板类型配置</h1>
          <p className="text-gray-600 mt-1">配置不同邮件模板类型的默认收件人和抄送人</p>
        </div>
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <label className="text-sm font-medium text-gray-700">项目筛选:</label>
            <select
              value={selectedProjectId}
              onChange={(e) => setSelectedProjectId(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm text-gray-900 bg-white"
            >
              <option value="">全部配置</option>
              <option value="null">仅全局配置</option>
              {projects.map(project => (
                <option key={project.id} value={project.id}>
                  {project.projectCode} - {project.projectName}
                </option>
              ))}
            </select>
          </div>
          <div className="flex space-x-2"> 
            <Button onClick={handleCreate}>
              新建配置
            </Button>
          </div>
        </div>
      </div>

      <div className="bg-white shadow rounded-lg">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>模板类型</TableHead>
              <TableHead>关联项目</TableHead>
              <TableHead>默认收件人</TableHead>
              <TableHead>默认抄送人</TableHead>
              <TableHead>说明</TableHead>
              <TableHead>更新时间</TableHead>
              <TableHead className="w-32">操作</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {configs.length === 0 ? (
              <TableRow>
                <TableCell colSpan={7} className="text-center text-gray-500 py-8">
                  暂无配置数据
                </TableCell>
              </TableRow>
            ) : (
              configs.map((config) => (
                <TableRow key={config.id}>
                  <TableCell>
                    <div className="font-medium">
                      {templateTypeMap[config.templateType as keyof typeof templateTypeMap] || config.templateType}
                    </div>
                    <div className="text-xs text-gray-500">{config.templateType}</div>
                  </TableCell>
                  <TableCell>
                    {config.project ? (
                      <div>
                        <div className="font-medium">{config.project.projectCode}</div>
                        <div className="text-xs text-gray-500">{config.project.projectName}</div>
                      </div>
                    ) : (
                      <span className="text-blue-600 font-medium">全局默认</span>
                    )}
                  </TableCell>
                  <TableCell>
                    {config.defaultRecipients && config.defaultRecipients.length > 0 ? (
                      <div className="text-sm">
                        {config.defaultRecipients.slice(0, 2).map((email, index) => (
                          <div key={index} className="text-gray-900">{email}</div>
                        ))}
                        {config.defaultRecipients.length > 2 && (
                          <div className="text-gray-500">+{config.defaultRecipients.length - 2}个</div>
                        )}
                      </div>
                    ) : (
                      <span className="text-gray-400">未配置</span>
                    )}
                  </TableCell>
                  <TableCell>
                    {config.defaultCcRecipients && config.defaultCcRecipients.length > 0 ? (
                      <div className="text-sm">
                        {config.defaultCcRecipients.slice(0, 2).map((email, index) => (
                          <div key={index} className="text-gray-900">{email}</div>
                        ))}
                        {config.defaultCcRecipients.length > 2 && (
                          <div className="text-gray-500">+{config.defaultCcRecipients.length - 2}个</div>
                        )}
                      </div>
                    ) : (
                      <span className="text-gray-400">未配置</span>
                    )}
                  </TableCell>
                  <TableCell>
                    <div className="text-sm text-gray-900 max-w-xs truncate">
                      {config.description || '-'}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="text-sm text-gray-500">
                      {new Date(config.updatedAt).toLocaleDateString('zh-CN')}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex space-x-2">
                      <button
                        onClick={() => handleEdit(config)}
                        className="text-blue-600 hover:text-blue-800 text-sm"
                      >
                        编辑
                      </button>
                      <button
                        onClick={() => handleDelete(config.id)}
                        className="text-red-600 hover:text-red-800 text-sm"
                      >
                        删除
                      </button>
                    </div>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {showForm && (
        <TemplateTypeConfigForm
          isOpen={showForm}
          onClose={() => setShowForm(false)}
          onSubmit={handleSubmit}
          config={editingConfig}
          mode={formMode}
        />
      )}
    </div>
  )
}
