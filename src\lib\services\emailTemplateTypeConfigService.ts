import { prisma } from '@/lib/db'
import { EmailTemplateType } from 'generated-prisma'

export interface EmailTemplateTypeConfigData {
  templateType: EmailTemplateType
  projectId?: number | null
  defaultRecipients?: string[]
  defaultCcRecipients?: string[]
  description?: string
}

export interface EmailTemplateTypeConfigWithProject {
  id: number
  templateType: EmailTemplateType
  projectId?: number | null
  defaultRecipients?: string[]
  defaultCcRecipients?: string[]
  description?: string
  createdAt: Date
  updatedAt: Date
  project?: {
    id: number
    projectCode: string
    projectName: string
  } | null
}

export interface GetConfigsOptions {
  projectId?: number | null
  templateType?: EmailTemplateType
  includeGlobal?: boolean
}

export class EmailTemplateTypeConfigService {
  private static instance: EmailTemplateTypeConfigService | null = null

  // 私有构造函数，防止外部直接实例化
  private constructor() {}

  /**
   * 获取单例实例
   */
  public static getInstance(): EmailTemplateTypeConfigService {
    if (!EmailTemplateTypeConfigService.instance) {
      EmailTemplateTypeConfigService.instance = new EmailTemplateTypeConfigService()
    }
    return EmailTemplateTypeConfigService.instance
  }

  /**
   * 获取配置列表
   */
  async getConfigs(options: GetConfigsOptions = {}): Promise<EmailTemplateTypeConfigWithProject[]> {
    try {
      const { projectId, templateType, includeGlobal = true } = options
      
      const where: any = {}
      
      // 项目筛选
      if (projectId !== undefined) {
        if (projectId === null) {
          // 只查询全局配置
          where.projectId = null
        } else {
          // 查询特定项目配置
          if (includeGlobal) {
            where.OR = [
              { projectId: projectId },
              { projectId: null }
            ]
          } else {
            where.projectId = projectId
          }
        }
      }
      
      // 模板类型筛选
      if (templateType) {
        where.templateType = templateType
      }
      
      const configs = await prisma.emailTemplateTypeConfig.findMany({
        where,
        include: {
          project: {
            select: {
              id: true,
              projectCode: true,
              projectName: true
            }
          }
        },
        orderBy: [
          { projectId: 'asc' }, // 全局配置优先
          { templateType: 'asc' }
        ]
      })
      
      return configs as EmailTemplateTypeConfigWithProject[]
    } catch (error) {
      console.error('获取邮件模板类型配置失败:', error)
      throw new Error(`获取配置失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 根据模板类型和项目获取最佳匹配的配置
   * 优先返回项目特定配置，如果没有则返回全局配置
   */
  async getBestMatchConfig(
    templateType: EmailTemplateType,
    projectId?: number
  ): Promise<EmailTemplateTypeConfigWithProject | null> {
    try {
      let config = null

      // 如果指定了项目ID，优先查找项目特定配置
      if (projectId) {
        config = await prisma.emailTemplateTypeConfig.findFirst({
          where: {
            templateType,
            projectId
          },
          include: {
            project: {
              select: {
                id: true,
                projectCode: true,
                projectName: true
              }
            }
          }
        })
      }

      // 如果没有找到项目特定配置，查找全局默认配置
      if (!config) {
        config = await prisma.emailTemplateTypeConfig.findFirst({
          where: {
            templateType,
            projectId: null
          }
        })
      }

      return config as EmailTemplateTypeConfigWithProject | null
    } catch (error) {
      console.error('获取最佳匹配配置失败:', error)
      throw new Error(`获取配置失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 创建配置
   */
  
  async createConfig(data: EmailTemplateTypeConfigData): Promise<EmailTemplateTypeConfigWithProject> {
    try {
      // 检查是否已存在该类型的配置（同一项目下）
      const existingConfig = await prisma.emailTemplateTypeConfig.findFirst({
        where: {
          templateType: data.templateType,
          projectId: data.projectId || null
        }
      })

      if (existingConfig) {
        const scope = data.projectId ? '该项目下' : '全局'
        throw new Error(`${scope}该邮件类型的配置已存在`)
      }

      // 如果指定了项目ID，验证项目是否存在
      if (data.projectId) {
        const project = await prisma.project.findUnique({
          where: { id: data.projectId }
        })
        
        if (!project) {
          throw new Error('指定的项目不存在')
        }
      }

      const newConfig = await prisma.emailTemplateTypeConfig.create({
        data: {
          templateType: data.templateType,
          projectId: data.projectId || null,
          defaultRecipients: data.defaultRecipients || [],
          defaultCcRecipients: data.defaultCcRecipients || [],
          description: data.description
        },
        include: {
          project: {
            select: {
              id: true,
              projectCode: true,
              projectName: true
            }
          }
        }
      })

      console.log('邮件模板类型配置创建成功:', {
        id: newConfig.id,
        templateType: newConfig.templateType,
        projectId: newConfig.projectId
      })

      return newConfig as EmailTemplateTypeConfigWithProject
    } catch (error) {
      console.error('创建邮件模板类型配置失败:', error)
      throw error instanceof Error ? error : new Error('创建配置失败')
    }
  }

  /**
   * 更新配置
   */
  async updateConfig(
    id: number, 
    data: Partial<EmailTemplateTypeConfigData>
  ): Promise<EmailTemplateTypeConfigWithProject> {
    try {
      // 检查配置是否存在
      const existingConfig = await prisma.emailTemplateTypeConfig.findUnique({
        where: { id },
        include: { project: true }
      })

      if (!existingConfig) {
        throw new Error('配置不存在')
      }

      // 如果更新了项目ID，验证项目是否存在
      if (data.projectId !== undefined && data.projectId !== null) {
        const project = await prisma.project.findUnique({
          where: { id: data.projectId }
        })
        
        if (!project) {
          throw new Error('指定的项目不存在')
        }
      }

      const updatedConfig = await prisma.emailTemplateTypeConfig.update({
        where: { id },
        data: {
          ...(data.templateType && { templateType: data.templateType }),
          ...(data.projectId !== undefined && { projectId: data.projectId }),
          ...(data.defaultRecipients !== undefined && { defaultRecipients: data.defaultRecipients }),
          ...(data.defaultCcRecipients !== undefined && { defaultCcRecipients: data.defaultCcRecipients }),
          ...(data.description !== undefined && { description: data.description })
        },
        include: {
          project: {
            select: {
              id: true,
              projectCode: true,
              projectName: true
            }
          }
        }
      })

      console.log('邮件模板类型配置更新成功:', {
        id: updatedConfig.id,
        templateType: updatedConfig.templateType,
        projectId: updatedConfig.projectId
      })

      return updatedConfig as EmailTemplateTypeConfigWithProject
    } catch (error) {
      console.error('更新邮件模板类型配置失败:', error)
      throw error instanceof Error ? error : new Error('更新配置失败')
    }
  }

  /**
   * 删除配置
   */
  async deleteConfig(id: number): Promise<void> {
    try {
      // 检查配置是否存在
      const existingConfig = await prisma.emailTemplateTypeConfig.findUnique({
        where: { id }
      })

      if (!existingConfig) {
        throw new Error('配置不存在')
      }

      await prisma.emailTemplateTypeConfig.delete({
        where: { id }
      })

      console.log('邮件模板类型配置删除成功:', {
        id,
        templateType: existingConfig.templateType,
        projectId: existingConfig.projectId
      })
    } catch (error) {
      console.error('删除邮件模板类型配置失败:', error)
      throw error instanceof Error ? error : new Error('删除配置失败')
    }
  }

  /**
   * 获取默认配置数据（用于创建模板时的自动填充）
   */
  async getDefaultConfigData(
    templateType: EmailTemplateType, 
    projectId?: number
  ): Promise<{ defaultRecipients: string[], defaultCcRecipients: string[] }> {
    try {
      const config = await this.getBestMatchConfig(templateType, projectId)
      
      return {
        defaultRecipients: config?.defaultRecipients as string[] || [],
        defaultCcRecipients: config?.defaultCcRecipients as string[] || []
      }
    } catch (error) {
      console.error('获取默认配置数据失败:', error)
      // 返回空配置而不是抛出错误，确保不影响模板创建流程
      return {
        defaultRecipients: [],
        defaultCcRecipients: []
      }
    }
  }
 
}

// 导出单例实例
export const emailTemplateTypeConfigService = EmailTemplateTypeConfigService.getInstance()
