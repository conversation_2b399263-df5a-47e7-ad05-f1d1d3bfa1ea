'use client'; 


export const loadOfficeJs = (): Promise<void> => { 
  return new Promise((resolve, reject) => {    
    const script = document.createElement('script')
    script.src = 'https://appsforoffice.cdn.partner.office365.cn/appsforoffice/lib/1/hosted/office.js' 
    script.onload = () => {
      setTimeout(() => {
        resolve();
      },500);
    }
    script.onerror = (error) => {
      reject(error)
    }
    document.head.appendChild(script);
  });
}
 

// 获取当前用户邮箱
export  const getCurrentUserEmail = () => {
  if (window.Office && Office.context.mailbox.userProfile) {
    return Office.context.mailbox.userProfile.emailAddress || '';
  }
  return '';
}

export const convertToEwsId = (itemId: string) => {
  return Office.context.mailbox.convertToEwsId(
    itemId,
    Office.MailboxEnums.RestVersion.v2_0, // 可选 REST 版本 
  );
}


export async function getCurEmailDetails() {
  const item = Office.context.mailbox.item;
  if(!item) throw new Error('未选择邮件');

  const subject = item.subject;
  const from = formatEmailAddress(item.from.displayName, item.from.emailAddress);
  const to = formatAddressList(item.to);
  const cc = formatAddressList(item.cc);
  const date = item.dateTimeCreated.toUTCString();

  // 获取原始HTML内容
  let body = await new Promise<string>((resolve, reject) => {
    item.body.getAsync("html", (res) =>
      res.status === Office.AsyncResultStatus.Succeeded
        ? resolve((res.value || '').replace(/<!--\[if.*?endif\]-->/gs, ''))
        : reject(res.error)
    );
  });

  // 获取所有附件信息
  const allAttachments = await Promise.all(
    (item.attachments || []).map(async (att) => {
      const file = await new Promise<Office.AttachmentContent>((resolve, reject) => {
        item.getAttachmentContentAsync(att.id, (res) =>
          res.status === Office.AsyncResultStatus.Succeeded
            ? resolve(res.value)
            : reject(res.error)
        );
      });

      return {
        name: att.name,
        size: att.size,
        isInline: att.isInline,
        id: att.id,
        contentType: att.contentType || "application/octet-stream",
        content: file.content, // base64
        encoding: file.format // should be "base64"
      };
    })
  );

  // 处理内联图片：删除HTML中的内联图片标签
  const inlineAttachments = allAttachments.filter(att => att.isInline);
  const processedInlineIds = new Set<string>();

  // 删除HTML中的内联图片引用
  inlineAttachments.forEach(att => {
    // 检查附件是否为图片类型
    if (att.contentType.startsWith('image/')) {
      // 可能的引用模式：包含cid:filename, cid:id, 或直接使用filename的img标签
      const patterns = [
        // 匹配包含cid:filename的img标签
        new RegExp(`<img[^>]*src=["']cid:${att.name.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}["'][^>]*>`, 'gi'),
        // 匹配包含cid:id的img标签
        new RegExp(`<img[^>]*src=["']cid:${att.id.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}["'][^>]*>`, 'gi'),
        // 匹配包含filename的img标签
        new RegExp(`<img[^>]*src=["']${att.name.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}["'][^>]*>`, 'gi')
      ];

      let wasReplaced = false;
      patterns.forEach(pattern => {
        const originalBody = body;
        body = body.replace(pattern, '');
        if (body !== originalBody) {
          wasReplaced = true;
        }
      });

      // 如果成功删除了引用，标记这个附件为已处理
      if (wasReplaced) {
        processedInlineIds.add(att.id);
      }
    }
  });

  // 过滤掉已处理的内联图片附件，只保留未处理的内联附件和所有非内联附件
  const attachments = allAttachments.filter(att =>
    !att.isInline || !processedInlineIds.has(att.id)
  );

  return { from, to, cc, subject, date, body, attachments };
}

function encodeDisplayName(name: string): string {
  if (!name || /^[\x00-\x7F]*$/.test(name)) {
    return name; // ASCII，无需编码
  }

  const utf8 = new TextEncoder().encode(name);
  const base64 = btoa(String.fromCharCode(...utf8));
  return `=?UTF-8?B?${base64}?=`;
}

function formatEmailAddress(displayName: string | undefined, email: string): string {
  if (!displayName || displayName === email) {
    return `<${email}>`;
  }

  const encodedName = encodeDisplayName(displayName);
  return `${encodedName} <${email}>`;
}

function formatAddressList(addresses: { displayName: string; emailAddress: string }[] | undefined): string {
  if (!addresses || addresses.length === 0) return "";
  return addresses
    .map(a => formatEmailAddress(a.displayName, a.emailAddress))
    .join(", ");
}