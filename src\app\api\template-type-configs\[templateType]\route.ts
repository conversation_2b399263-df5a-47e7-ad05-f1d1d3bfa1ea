import { NextRequest, NextResponse } from 'next/server'
import { getCurrentUser, hasPermission } from '@/lib/auth'
import { EmailTemplateType } from 'generated-prisma'
import { emailTemplateTypeConfigService } from '@/lib/services'

/**
 * 获取特定邮件模板类型的默认配置
 * 支持项目特定配置，优先返回项目配置，如果没有则返回全局配置
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { templateType: string } }
) {
  try {
    const user = await getCurrentUser(request)
    if (!user || !hasPermission(user.roles, ['PM', 'Triage'])) {
      return NextResponse.json({
        success: false,
        error: 'Unauthorized'
      }, { status: 401 })
    }

    const { templateType } = params
    const { searchParams } = new URL(request.url)
    const projectIdParam = searchParams.get('projectId')

    // 验证模板类型
    const validTypes = ['CONFIRMATION', 'SAFETY_NOTIFICATION', 'INTERNAL_FORWARD', 'WM_FORWARD']
    if (!validTypes.includes(templateType)) {
      return NextResponse.json({
        success: false,
        error: '无效的邮件模板类型'
      }, { status: 400 })
    }

    const projectId = projectIdParam ? parseInt(projectIdParam) : undefined
    const config = await emailTemplateTypeConfigService.getBestMatchConfig(
      templateType as EmailTemplateType,
      projectId
    )

    if (!config) {
      // 如果没有配置，返回空的默认配置
      return NextResponse.json({
        success: true,
        data: {
          config: {
            templateType,
            projectId: projectId || null,
            defaultRecipients: [],
            defaultCcRecipients: [],
            description: null
          }
        }
      })
    }

    return NextResponse.json({
      success: true,
      data: { config }
    })

  } catch (error) {
    console.error('Get template type config failed:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '获取配置失败'
    }, { status: 500 })
  }
}
