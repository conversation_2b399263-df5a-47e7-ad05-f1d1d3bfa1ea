'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/Button'

export default function TestParallelTasksPage() {
  const [messages, setMessages] = useState<string[]>([])
  const [isConnected, setIsConnected] = useState(false)
  const [processingData, setProcessingData] = useState<any>(null)

  useEffect(() => {
    connectSSE()
    return () => {
      disconnectSSE()
    }
  }, [])

  let eventSource: EventSource | null = null

  const connectSSE = () => {
    try {
      eventSource = new EventSource('/api/tasks/status-stream')
      
      eventSource.onopen = () => {
        setIsConnected(true)
        addMessage('SSE连接已建立')
      }

      eventSource.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data)
          addMessage(`收到消息: ${JSON.stringify(data, null, 2)}`)
          
          if (data.type === 'CURRENT_STATUS') {
            setProcessingData(data.data)
          }
        } catch (error) {
          addMessage(`解析消息失败: ${error}`)
        }
      }

      eventSource.onerror = (error) => {
        setIsConnected(false)
        addMessage(`SSE连接错误: ${error}`)
      }
    } catch (error) {
      addMessage(`建立SSE连接失败: ${error}`)
    }
  }

  const disconnectSSE = () => {
    if (eventSource) {
      eventSource.close()
      eventSource = null
      setIsConnected(false)
    }
  }

  const addMessage = (message: string) => {
    setMessages(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`])
  }

  const handleProcessTasks = async () => {
    try {
      addMessage('开始处理任务...')
      
      const response = await fetch('/api/jobs/process-tasks', {
        method: 'POST',
        credentials: 'include'
      })

      const result = await response.json()
      addMessage(`处理任务响应: ${JSON.stringify(result, null, 2)}`)
    } catch (error) {
      addMessage(`处理任务失败: ${error}`)
    }
  }

  const handleGetStatus = async () => {
    try {
      const response = await fetch('/api/jobs/process-tasks', {
        credentials: 'include'
      })

      const result = await response.json()
      addMessage(`获取状态响应: ${JSON.stringify(result, null, 2)}`)
      setProcessingData(result.data)
    } catch (error) {
      addMessage(`获取状态失败: ${error}`)
    }
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <h1 className="text-2xl font-bold">并行任务处理测试</h1>
      
      <div className="flex space-x-4">
        <Button onClick={handleProcessTasks}>
          处理任务
        </Button>
        <Button onClick={handleGetStatus} variant="outline">
          获取状态
        </Button>
        <Button onClick={connectSSE} variant="outline" disabled={isConnected}>
          连接SSE
        </Button>
        <Button onClick={disconnectSSE} variant="outline" disabled={!isConnected}>
          断开SSE
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-4">
          <h2 className="text-lg font-semibold">连接状态</h2>
          <div className="p-4 bg-gray-50 rounded-lg">
            <div className="flex items-center space-x-2">
              <div className={`w-3 h-3 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`}></div>
              <span>{isConnected ? 'SSE已连接' : 'SSE未连接'}</span>
            </div>
          </div>

          {processingData && (
            <div className="space-y-2">
              <h3 className="font-medium">处理状态</h3>
              <div className="p-4 bg-blue-50 rounded-lg">
                <div>正在处理任务数: {processingData.processingTaskCount || 0}</div>
                <div>处理中任务ID: {JSON.stringify(processingData.processingTaskIds || [])}</div>
                {processingData.processingStats && (
                  <div className="mt-2">
                    <div>总任务: {processingData.processingStats.totalTasks}</div>
                    <div>待处理: {processingData.processingStats.pendingTasks}</div>
                    <div>处理中: {processingData.processingStats.processingTasks}</div>
                    <div>已完成: {processingData.processingStats.completedTasks}</div>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>

        <div className="space-y-4">
          <h2 className="text-lg font-semibold">消息日志</h2>
          <div className="h-96 overflow-y-auto p-4 bg-gray-50 rounded-lg">
            {messages.map((message, index) => (
              <div key={index} className="text-sm mb-2 font-mono">
                {message}
              </div>
            ))}
          </div>
          <Button 
            onClick={() => setMessages([])} 
            variant="outline" 
            size="sm"
          >
            清空日志
          </Button>
        </div>
      </div>
    </div>
  )
}
