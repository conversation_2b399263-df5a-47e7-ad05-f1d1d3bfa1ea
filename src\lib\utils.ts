import { type ClassValue, clsx } from 'clsx'
import { twMerge } from 'tailwind-merge' 
import type { EmailDetailsForEml, AttachmentData } from '@/lib/services/emlFileService'

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatDate(date: string | Date) {
  return new Intl.DateTimeFormat('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
  }).format(new Date(date))
}

/**
 * 格式化中文日期（年月日格式）
 * @param date 日期对象或日期字符串
 * @returns 格式化后的中文日期，如 "2024年1月15日"
 */
export function formatChineseDate(date: string | Date): string {
  const d = new Date(date)
  const year = d.getFullYear()
  const month = d.getMonth() + 1
  const day = d.getDate()
  return `${year}年${month}月${day}日`
}

/**
 * 获取指定日期的第二天并格式化为中文日期
 * @param date 日期对象或日期字符串
 * @returns 第二天的中文日期格式
 */
export function getNextDayChineseDate(date: string | Date): string {
  const d = new Date(date)
  d.setDate(d.getDate() + 1)
  return formatChineseDate(d)
}

export function formatFileSize(bytes: number) {
  if (bytes === 0) return '0 Bytes'

  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/**
 * 格式化持续时间（毫秒）为可读格式
 * @param milliseconds 毫秒数
 * @returns 格式化后的时间字符串
 */
export function formatDuration(milliseconds: number): string {
  if (milliseconds < 1000) {
    return `${milliseconds}ms`
  }

  const seconds = Math.floor(milliseconds / 1000)
  const minutes = Math.floor(seconds / 60)
  const hours = Math.floor(minutes / 60)
  const days = Math.floor(hours / 24)

  if (days > 0) {
    return `${days}天 ${hours % 24}小时 ${minutes % 60}分钟`
  } else if (hours > 0) {
    return `${hours}小时 ${minutes % 60}分钟`
  } else if (minutes > 0) {
    return `${minutes}分钟 ${seconds % 60}秒`
  } else {
    return `${seconds}秒`
  }
}

export function getStatusColor(status: string) {
  switch (status.toLowerCase()) {
    case 'completed':
    case 'success':
      return 'badge-success'
    case 'processing':
    case 'in_progress':
      return 'badge-warning'
    case 'failed':
    case 'error':
      return 'badge-danger'
    case 'pending':
      return 'badge-info'
    default:
      return 'badge-gray'
  }
}

export function getStatusText(status: string) {
  switch (status) {
    case 'RECEIVED':
      return '已接收'
    case 'PROCESSING':
      return '处理中'
    case 'CONFIRMED':
      return '已确认'
    case 'FORWARDED':
      return '已转发'
    case 'COMPLETED':
      return '已完成'
    case 'PENDING':
      return '待处理'
    case 'IN_PROGRESS':
      return '进行中'
    case 'FAILED':
      return '失败'
    case 'SKIPPED':
      return '已跳过'
    default:
      return status
  }
}

export function getRoleText(role: string) {
  switch (role) {
    case 'WM':
      return 'Workflow Manager'
    case 'Triage':
      return 'Triage'
    case 'PM':
      return 'Project Manager'
    case 'DE':
      return 'Data Entry'
    case 'QC':
      return 'Quality Control'
    default:
      return role
  }
}

export function getRolesText(roles: string[] | string): string {
  if (typeof roles === 'string') {
    return getRoleText(roles)
  }

  if (Array.isArray(roles)) {
    return roles.map(role => getRoleText(role)).join(', ')
  }

  return ''
}

export function getReportTypeText(type: string) {
  switch (type) {
    case 'INITIAL':
      return '首次报告'
    case 'FOLLOW_UP':
      return '随访报告'
    case 'QUERY_RESPONSE':
      return '质疑回复'
    default:
      return type
  }
}

export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null
  
  return (...args: Parameters<T>) => {
    if (timeout) {
      clearTimeout(timeout)
    }
    
    timeout = setTimeout(() => {
      func(...args)
    }, wait)
  }
}



export function buildMimeEmail({ from, to, cc, subject, date, body, attachments }: EmailDetailsForEml) {
  const boundary = "----=_NextPart_" + Date.now();
  const headers = [
    `From: ${from}`,
    `To: ${to}`,
    cc ? `Cc: ${cc}` : "",
    `Subject: ${subject}`,
    `Date: ${date}`,
    `MIME-Version: 1.0`,
    `Content-Type: multipart/mixed; boundary="${boundary}"`
  ].filter(Boolean).join("\r\n");

  const parts = [];

  // HTML body
  parts.push([
    `--${boundary}`,
    `Content-Type: text/html; charset="UTF-8"`,
    `Content-Transfer-Encoding: quoted-printable`,
    ``,
    body
  ].join("\r\n"));

  // Attachments
  attachments.forEach(att => {
    parts.push([
      `--${boundary}`,
      `Content-ID: <${att.name}>`,
      `Content-Type: ${att.contentType}; name="${att.name}"`,
      `Content-Transfer-Encoding: base64`,
      `Content-Disposition: ${att.isInline ? 'inline' : 'attachment'}; filename="${att.name}"`,
      ``,
      att.content
    ].join("\r\n"));
  });

  // End boundary
  parts.push(`--${boundary}--`);

  return `${headers}\r\n\r\n${parts.join("\r\n")}`;
}

/**
 * 解析EML文件内容，返回与buildMimeEmail兼容的数据结构
 */
export function parseEmlContent(emlContent: string): EmailDetailsForEml {
  // 分离邮件头和邮件体
  const [headerSection, ...bodyParts] = emlContent.split('\r\n\r\n');
  const bodyContent = bodyParts.join('\r\n\r\n');

  // 解析邮件头
  const headers = parseEmailHeaders(headerSection);

  // 提取基本信息
  const from = headers.from || '';
  const to = headers.to || '';
  const cc = headers.cc || '';
  const subject = headers.subject || '';
  const date = headers.date || new Date().toUTCString();

  // 解析邮件体和附件
  const { body, attachments } = parseEmailBody(bodyContent, headers);

  return {
    from,
    to,
    cc,
    subject,
    date,
    body,
    attachments
  };
}

/**
 * 解析邮件头部信息
 */
function parseEmailHeaders(headerSection: string): Record<string, string> {
  const headers: Record<string, string> = {};
  const lines = headerSection.split('\r\n');

  let currentHeader = '';
  let currentValue = '';

  for (const line of lines) {
    if (line.match(/^[A-Za-z-]+:/)) {
      // 保存上一个头部
      if (currentHeader) {
        headers[currentHeader.toLowerCase()] = currentValue.trim();
      }

      // 开始新的头部
      const [header, ...valueParts] = line.split(':');
      currentHeader = header.trim();
      currentValue = valueParts.join(':').trim();
    } else if (line.startsWith(' ') || line.startsWith('\t')) {
      // 续行
      currentValue += ' ' + line.trim();
    }
  }

  // 保存最后一个头部
  if (currentHeader) {
    headers[currentHeader.toLowerCase()] = currentValue.trim();
  }

  return headers;
}

/**
 * 解析邮件体和附件
 */
function parseEmailBody(bodyContent: string, headers: Record<string, string>): {
  body: string;
  attachments: AttachmentData[]
} {
  const contentType = headers['content-type'] || '';

  // 如果不是multipart，直接返回body
  if (!contentType.includes('multipart')) {
    return {
      body: bodyContent,
      attachments: []
    };
  }

  // 提取boundary
  const boundaryMatch = contentType.match(/boundary="?([^";\s]+)"?/);
  if (!boundaryMatch) {
    return {
      body: bodyContent,
      attachments: []
    };
  }

  const boundary = boundaryMatch[1];
  const parts = bodyContent.split(`--${boundary}`);

  let body = '';
  const attachments: AttachmentData[] = [];

  for (const part of parts) {
    if (!part.trim() || part.trim() === '--') continue;

    const [partHeaders, ...partBodyParts] = part.split('\r\n\r\n');
    const partBody = partBodyParts.join('\r\n\r\n');

    if (!partHeaders || !partBody) continue;

    const partHeadersObj = parseEmailHeaders(partHeaders);
    const contentType = partHeadersObj['content-type'] || '';
    const contentDisposition = partHeadersObj['content-disposition'] || '';

    // 判断是否为附件
    if (contentDisposition.includes('attachment') || contentDisposition.includes('inline')) {
      // 解析附件
      const attachment = parseAttachment(partHeadersObj, partBody);
      if (attachment) {
        attachments.push(attachment);
      }
    } else if (contentType.includes('text/html')) {
      // HTML正文
      body = partBody.trim();
    } else if (contentType.includes('text/plain') && !body) {
      // 纯文本正文（如果还没有HTML正文）
      body = partBody.trim();
    }
  }

  return { body, attachments };
}

/**
 * 解析附件信息
 */
function parseAttachment(headers: Record<string, string>, content: string): AttachmentData | null {
  const contentType = headers['content-type'] || 'application/octet-stream';
  const contentDisposition = headers['content-disposition'] || '';
  const contentId = headers['content-id'] || '';
  const transferEncoding = headers['content-transfer-encoding'] || '';

  // 提取文件名
  let filename = '';
  const filenameMatch = contentDisposition.match(/filename="?([^";\s]+)"?/) ||
                       contentType.match(/name="?([^";\s]+)"?/);
  if (filenameMatch) {
    filename = filenameMatch[1];
  }

  if (!filename) {
    // 如果没有文件名，尝试从Content-ID生成
    const idMatch = contentId.match(/<(.+)>/);
    filename = idMatch ? idMatch[1] : `attachment_${Date.now()}`;
  }

  // 判断是否为内联附件
  const isInline = contentDisposition.includes('inline');

  // 处理内容编码
  let processedContent = content.trim();
  if (transferEncoding.toLowerCase() === 'base64') {
    // 内容已经是base64编码，直接使用
    processedContent = processedContent.replace(/\r\n/g, '');
  } else {
    // 其他编码转换为base64
    processedContent = Buffer.from(processedContent).toString('base64');
  }

  return {
    name: filename,
    size: Math.ceil(processedContent.length * 0.75), // base64大小估算
    contentType: contentType.split(';')[0].trim(),
    content: processedContent,
    isInline,
    id: contentId.replace(/[<>]/g, '') || filename,
    encoding: 'base64'
  };
}
 