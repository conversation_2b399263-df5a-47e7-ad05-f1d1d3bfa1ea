import { useState, useEffect, useCallback } from 'react'
import { emailTemplateTypeConfigService } from '@/lib/services'

export interface EmailTemplateTypeConfig {
  id: number
  templateType: string
  projectId?: number
  defaultRecipients?: string[]
  defaultCcRecipients?: string[]
  description?: string
  createdAt: string
  updatedAt: string
  project?: {
    id: number
    projectCode: string
    projectName: string
  }
}

export interface UseEmailTemplateTypeConfigsOptions {
  projectId?: string
  autoFetch?: boolean
}

export interface UseEmailTemplateTypeConfigsReturn {
  configs: EmailTemplateTypeConfig[]
  isLoading: boolean
  error: string | null
  fetchConfigs: () => Promise<void>
  createConfig: (configData: Partial<EmailTemplateTypeConfig>) => Promise<void>
  updateConfig: (configData: Partial<EmailTemplateTypeConfig>) => Promise<void>
  deleteConfig: (id: number) => Promise<void> 
}

export function useEmailTemplateTypeConfigs(
  options: UseEmailTemplateTypeConfigsOptions = {}
): UseEmailTemplateTypeConfigsReturn {
  const { projectId, autoFetch = true } = options
  
  const [configs, setConfigs] = useState<EmailTemplateTypeConfig[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // 获取配置列表
  const fetchConfigs = useCallback(async () => {
    try {
      setIsLoading(true)
      setError(null)
      
      const params = new URLSearchParams()
      if (projectId) {
        params.append('projectId', projectId)
      }
      
      const url = `/api/template-type-configs${params.toString() ? `?${params}` : ''}`
      const response = await fetch(url)
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }
      
      const result = await response.json()
      if (result.success) {
        setConfigs(result.data.configs)
      } else {
        throw new Error(result.error || '获取配置失败')
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '获取配置失败'
      setError(errorMessage)
      console.error('Failed to fetch configs:', err)
    } finally {
      setIsLoading(false)
    }
  }, [projectId])

  // 创建配置
  const createConfig = useCallback(async (configData: Partial<EmailTemplateTypeConfig>) => {
    try {
      setError(null)
      
      const response = await fetch('/api/template-type-configs', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(configData)
      })

      if (!response.ok) {
        const result = await response.json()
        throw new Error(result.error || '创建配置失败')
      }

      const result = await response.json()
      if (!result.success) {
        throw new Error(result.error || '创建配置失败')
      }

      // 重新获取配置列表
      await fetchConfigs()
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '创建配置失败'
      setError(errorMessage)
      throw err
    }
  }, [fetchConfigs])

  // 更新配置
  const updateConfig = useCallback(async (configData: Partial<EmailTemplateTypeConfig>) => {
    try {
      setError(null)
      
      const response = await fetch('/api/template-type-configs', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(configData)
      })

      if (!response.ok) {
        const result = await response.json()
        throw new Error(result.error || '更新配置失败')
      }

      const result = await response.json()
      if (!result.success) {
        throw new Error(result.error || '更新配置失败')
      }

      // 重新获取配置列表
      await fetchConfigs()
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '更新配置失败'
      setError(errorMessage)
      throw err
    }
  }, [fetchConfigs])

  // 删除配置
  const deleteConfig = useCallback(async (id: number) => {
    try {
      setError(null)
      
      const response = await fetch(`/api/template-type-configs?id=${id}`, {
        method: 'DELETE'
      })

      if (!response.ok) {
        const result = await response.json()
        throw new Error(result.error || '删除配置失败')
      }

      const result = await response.json()
      if (!result.success) {
        throw new Error(result.error || '删除配置失败')
      }

      // 重新获取配置列表
      await fetchConfigs()
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '删除配置失败'
      setError(errorMessage)
      throw err
    }
  }, [fetchConfigs])
 

  // 自动获取数据
  useEffect(() => {
    if (autoFetch) {
      fetchConfigs()
    }
  }, [autoFetch, fetchConfigs])

  return {
    configs,
    isLoading,
    error,
    fetchConfigs,
    createConfig,
    updateConfig,
    deleteConfig, 
  }
}
