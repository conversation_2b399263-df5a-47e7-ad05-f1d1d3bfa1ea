'use client'

import { useState, useEffect } from 'react'
import { <PERSON><PERSON>, Mo<PERSON>Footer } from '@/components/ui/Modal'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'

interface EmailTemplateTypeConfig {
  id?: number
  templateType: string
  projectId?: number
  defaultRecipients?: string[]
  defaultCcRecipients?: string[]
  description?: string
  project?: {
    id: number
    projectCode: string
    projectName: string
  }
}

interface Project {
  id: number
  projectCode: string
  projectName: string
}

interface TemplateTypeConfigFormProps {
  isOpen: boolean
  onClose: () => void
  onSubmit: (config: Partial<EmailTemplateTypeConfig>) => Promise<void>
  config?: EmailTemplateTypeConfig | null
  mode: 'create' | 'edit'
}

const templateTypeOptions = [
  { value: 'CONFIRMATION', label: '确认回复' },
  { value: 'SAFETY_NOTIFICATION', label: '安全性通知' },
  { value: 'INTERNA<PERSON>_FORWARD', label: '内部转发' },
  { value: 'WM_FORWARD', label: 'WM转发' }
]

export function TemplateTypeConfigForm({ isOpen, onClose, onSubmit, config, mode }: TemplateTypeConfigFormProps) {
  const [formData, setFormData] = useState<Partial<EmailTemplateTypeConfig>>({
    templateType: 'CONFIRMATION',
    projectId: undefined,
    defaultRecipients: [],
    defaultCcRecipients: [],
    description: ''
  })
  const [projects, setProjects] = useState<Project[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [errors, setErrors] = useState<Record<string, string>>({})

  // 处理邮件地址数组输入的辅助函数
  const parseEmailList = (emailString: string): string[] => {
    if (!emailString.trim()) return []
    return emailString
      .split(/[,;\s]+/)
      .map(email => email.trim())
      .filter(email => email.length > 0)
  }

  const formatEmailList = (emails: string[]): string => {
    return emails.join(', ')
  }

  // 获取项目列表
  const fetchProjects = async () => {
    try {
      const response = await fetch('/api/projects')
      if (response.ok) {
        const result = await response.json()
        if (result.success) {
          setProjects(result.data.projects)
        }
      }
    } catch (error) {
      console.error('Failed to fetch projects:', error)
    }
  }

  useEffect(() => {
    if (isOpen) {
      fetchProjects()
    }
  }, [isOpen])

  useEffect(() => {
    if (config && mode === 'edit') {
      setFormData({
        id: config.id,
        templateType: config.templateType,
        projectId: config.projectId,
        defaultRecipients: config.defaultRecipients || [],
        defaultCcRecipients: config.defaultCcRecipients || [],
        description: config.description || ''
      })
    } else {
      setFormData({
        templateType: 'CONFIRMATION',
        projectId: undefined,
        defaultRecipients: [],
        defaultCcRecipients: [],
        description: ''
      })
    }
    setErrors({})
  }, [config, mode, isOpen])

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {}

    if (!formData.templateType) {
      newErrors.templateType = '请选择邮件模板类型'
    }

    // 验证收件人邮箱格式
    if (formData.defaultRecipients && formData.defaultRecipients.length > 0) {
      const invalidEmails = formData.defaultRecipients.filter(email =>
        email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)
      )
      if (invalidEmails.length > 0) {
        newErrors.defaultRecipients = '请输入有效的收件人邮箱地址'
      }
    }

    // 验证抄送人邮箱格式
    if (formData.defaultCcRecipients && formData.defaultCcRecipients.length > 0) {
      const invalidCcEmails = formData.defaultCcRecipients.filter(email =>
        email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)
      )
      if (invalidCcEmails.length > 0) {
        newErrors.defaultCcRecipients = '请输入有效的抄送人邮箱地址'
      }
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    setIsLoading(true)
    try {
      await onSubmit(formData)
      onClose()
    } catch (error) {
      console.error('Submit failed:', error)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={mode === 'create' ? '新建邮件模板类型配置' : '编辑邮件模板类型配置'}
      size="lg"
    >
      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              邮件模板类型 *
            </label>
            <select
              value={formData.templateType || ''}
              onChange={(e) => setFormData(prev => ({ ...prev, templateType: e.target.value }))}
              disabled={mode === 'edit'} // 编辑时不允许修改类型
              className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm text-gray-900 bg-white disabled:bg-gray-100"
            >
              {templateTypeOptions.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
            {errors.templateType && (
              <p className="text-sm text-red-600 mt-1">{errors.templateType}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              关联项目 (可选)
            </label>
            <select
              value={formData.projectId || ''}
              onChange={(e) => setFormData(prev => ({ ...prev, projectId: e.target.value ? parseInt(e.target.value) : undefined }))}
              disabled={mode === 'edit'} // 编辑时不允许修改项目
              className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm text-gray-900 bg-white disabled:bg-gray-100"
            >
              <option value="">全局默认配置</option>
              {projects.map(project => (
                <option key={project.id} value={project.id}>
                  {project.projectCode} - {project.projectName}
                </option>
              ))}
            </select>
            <p className="text-sm text-gray-500 mt-1">
              选择项目可创建项目特定配置，留空则为全局默认配置
            </p>
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            默认收件人邮箱地址
          </label>
          <textarea
            value={formatEmailList(formData.defaultRecipients || [])}
            onChange={(e) => setFormData(prev => ({
              ...prev,
              defaultRecipients: parseEmailList(e.target.value)
            }))}
            rows={3}
            className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm text-gray-900 bg-white"
            placeholder="例如：<EMAIL>, <EMAIL>"
          />
          {errors.defaultRecipients && (
            <p className="text-sm text-red-600 mt-1">{errors.defaultRecipients}</p>
          )}
          <p className="text-sm text-gray-500 mt-1">
            多个邮箱地址请用英文逗号分隔。这些地址将作为创建此类型模板时的默认收件人。
          </p>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            默认抄送人邮箱地址
          </label>
          <textarea
            value={formatEmailList(formData.defaultCcRecipients || [])}
            onChange={(e) => setFormData(prev => ({
              ...prev,
              defaultCcRecipients: parseEmailList(e.target.value)
            }))}
            rows={2}
            className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm text-gray-900 bg-white"
            placeholder="例如：<EMAIL>, <EMAIL>"
          />
          {errors.defaultCcRecipients && (
            <p className="text-sm text-red-600 mt-1">{errors.defaultCcRecipients}</p>
          )}
          <p className="text-sm text-gray-500 mt-1">
            多个邮箱地址请用英文逗号分隔。可选字段。
          </p>
        </div>

        <div>
          <Input
            label="配置说明"
            value={formData.description || ''}
            onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
            placeholder="描述此邮件类型的用途和收件人规则"
          />
        </div>

        <ModalFooter>
          <Button
            type="button"
            variant="secondary"
            onClick={onClose}
            disabled={isLoading}
          >
            取消
          </Button>
          <Button
            type="submit"
            disabled={isLoading}
          >
            {isLoading ? '保存中...' : (mode === 'create' ? '创建' : '更新')}
          </Button>
        </ModalFooter>
      </form>
    </Modal>
  )
}
