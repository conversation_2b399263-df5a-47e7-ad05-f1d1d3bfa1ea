import { prisma } from '@/lib/db'
import { PDFDocument, rgb } from 'pdf-lib'
import pdf from 'html-pdf'
import { promises as fs } from 'fs'
import path from 'path'
import crypto from 'crypto'
import { ReportAttachmentType } from 'generated-prisma' 
import { uploadDir } from '@/config'

export interface AttachmentInfo {
  name: string
  size: number
  contentType: string
  content: string // base64
  isInline?: boolean
}

export interface SaveAttachmentParams {
  reportId: number
  fileName: string
  filePath: string
  type: ReportAttachmentType
  originalFileName?: string
  fileSize?: number
  fileType?: string
  mimeType?: string
}

export interface MergedPDFResult {
  filePath: string
  fileName: string
  fileSize: number
  pageCount: number
}

/**
 * 文件附件服务
 * 负责文件的存储、PDF合并、邮件正文转PDF等功能
 */
export class FileAttachmentService {
  private readonly uploadDir = path.join(uploadDir, 'sae-reports')
  
  constructor() {
    this.ensureUploadDir()
  }
  
  /**
   * 确保上传目录存在
   */
  private async ensureUploadDir(): Promise<void> {
    try {
      await fs.mkdir(this.uploadDir, { recursive: true })
    } catch (error) {
      console.error('创建上传目录失败:', error)
    }
  }
  
  /**
   * 保存附件记录到数据库
   */ 
  async saveAttachment(params: SaveAttachmentParams): Promise<number> {
    const { reportId, fileName, filePath, type, originalFileName, fileSize, fileType, mimeType } = params
    
    try {
      // 计算文件hash
      let fileHash: string | undefined
      try {
        const fileBuffer = await fs.readFile(filePath)
        fileHash = crypto.createHash('md5').update(fileBuffer).digest('hex')
      } catch (error) {
        console.warn('计算文件hash失败:', error)
      }
      
      const attachment = await prisma.reportAttachment.create({
        data: {
          reportId,
          fileName,
          originalFileName: originalFileName || fileName,
          filePath,
          type,
          fileSize: fileSize ? BigInt(fileSize) : null,
          fileType: fileType || null,
          mimeType: mimeType || null,
          fileHash: fileHash || null
        }
      })
      
      console.log('附件记录保存成功:', { id: attachment.id, fileName, type })
      return attachment.id
      
    } catch (error) {
      console.error('保存附件记录失败:', error)
      throw new Error(`保存附件记录失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }
  
  /**
   * 将邮件正文转换为PDF
   */
  // 将 HTML 转换为 PDF Buffer
  htmlToPDFBuffer(emailContent: string, options = {}): Promise<Buffer> {
    return new Promise((resolve, reject) => { 
      pdf.create(emailContent, {
        format: 'A4',
        orientation: 'portrait',
        border: {
          top: '15mm',
          right: '15mm',
          bottom: '15mm',
          left: '15mm'
        },
        header: {
          height: '10mm'
        },
        footer: {
          height: '10mm'
        },
        ...options
      }).toBuffer((err, buffer) => {
        if (err) {
          reject(err);
        } else {
          resolve(buffer);
        }
      });
    });
  }
  /**
   * 合并所有附件为一个PDF
   */
  async mergeAttachmentsToPDF(
    emailBodyPDF: Buffer,
    attachments: AttachmentInfo[],
    reportId: number
  ): Promise<MergedPDFResult> {
    try {
      console.log('开始合并PDF附件...')
      
      // 创建新的PDF文档
      const mergedPdf = await PDFDocument.create()
      
      // 首先添加邮件正文PDF
      const emailPdf = await PDFDocument.load(emailBodyPDF)
      const emailPages = await mergedPdf.copyPages(emailPdf, emailPdf.getPageIndices())
      emailPages.forEach((page) => mergedPdf.addPage(page))
      
      // 添加其他PDF附件
      const pdfAttachments = attachments.filter(att => 
        att.contentType === 'application/pdf' || att.name.toLowerCase().endsWith('.pdf')
      )
      
      for (const attachment of pdfAttachments) {
        try {
          const pdfBuffer = Buffer.from(attachment.content, 'base64')
          const pdf = await PDFDocument.load(pdfBuffer)
          const pages = await mergedPdf.copyPages(pdf, pdf.getPageIndices())
          pages.forEach((page) => mergedPdf.addPage(page))
          
          console.log(`已合并PDF附件: ${attachment.name}`)
        } catch (error) {
          console.warn(`合并PDF附件失败: ${attachment.name}`, error)
        }
      }
      
      // 保存合并后的PDF
      const pdfBytes = await mergedPdf.save()
      const fileName = `merged_report_${reportId}_${Date.now()}.pdf`
      const filePath = path.join(this.uploadDir, fileName)
      
      await fs.writeFile(filePath, pdfBytes)
      
      const result: MergedPDFResult = {
        filePath,
        fileName,
        fileSize: pdfBytes.length,
        pageCount: mergedPdf.getPageCount()
      }
      
      console.log('PDF合并完成:', result)
      return result
      
    } catch (error) {
      console.error('合并PDF失败:', error)
      throw new Error(`合并PDF失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

    
  /**
   * 删除文件
   */
  async deleteFile(filePath: string): Promise<void> {
    try {
      await fs.unlink(filePath)
      console.log('文件删除成功:', filePath)
    } catch (error) {
      console.warn('删除文件失败:', filePath, error)
    }
  }
  
  /**
   * 获取文件信息
   */
  async getFileInfo(filePath: string) {
    try {
      const stats = await fs.stat(filePath)
      return {
        size: stats.size,
        createdAt: stats.birthtime,
        modifiedAt: stats.mtime
      }
    } catch (error) {
      console.error('获取文件信息失败:', error)
      return null
    }
  }
}

// 导出单例实例
export const fileAttachmentService = new FileAttachmentService()
