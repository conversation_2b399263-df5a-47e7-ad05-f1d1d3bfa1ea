import { prisma } from '@/lib/db'
import { PDFProcessorService } from './pdfProcessorService'
import { projectService } from './projectService'
import { AIExtractor } from './aiExtractor'
import { saeReportService } from './saeReportService'
import { fileAttachmentService } from './fileAttachmentService'
import { emailTemplateService } from './emailTemplateService'
import { notificationService } from './notificationService'
import { personnelAssignmentService } from './personnelAssignmentService'
import { TaskStatus, TaskPriority, UserRole, ReportAttachmentType, ReportStatus } from 'generated-prisma'
import type { Prisma } from 'generated-prisma'; 
import { formatChineseDate, getNextDayChineseDate } from '@/lib/utils'
import logger, { taskLog, emailLog, dbLog } from '@/lib/logger'
import { emlFileService } from './emlFileService'

type Task = Prisma.TaskGetPayload<{
  include: {
    createdBy: true; 
  }
}>
 

export interface TaskProcessingResult {
  success: boolean
  processedCount: number
  failedCount: number
  results: Array<{
    taskId: number
    success: boolean
    error?: string
    assignedProject?: string
    assignedPersonnel?: {
      processor?: string
      dataEntry?: string
      qualityControl?: string
    }
  }>
}

export interface ProcessingStats {
  totalTasks: number
  pendingTasks: number
  processingTasks: number
  completedTasks: number
  failedTasks: number
}

export class TaskProcessorService {
  private aiExtractor: AIExtractor
  private processingTasks = new Set<number>() // 正在处理的任务ID集合
  private taskStatusListeners = new Map<string, (data: any) => void>() // SSE监听器

  constructor() {
    this.aiExtractor = new AIExtractor()
  }

  /**
   * 异步处理所有待处理任务（并行执行）
   */
  async processAllPendingTasksAsync(): Promise<{ success: boolean; message: string; startedTasks: number }> {
    logger.info('🚀 开始异步处理待处理任务...', { action: 'async_batch_task_start' })

    try {
      // 查询待处理任务
      const pendingTasks = await this.getPendingTasks()
      logger.info(`找到 ${pendingTasks.length} 个待处理任务`, {
        action: 'async_batch_task_query',
        taskCount: pendingTasks.length
      })

      if (pendingTasks.length === 0) {
        logger.info('没有待处理任务', { action: 'async_batch_task_empty' })
        return {
          success: true,
          message: '没有待处理任务',
          startedTasks: 0
        }
      }

      let startedTasks = 0

      // 并行启动任务处理
      for (const task of pendingTasks) {
        // 检查任务是否已在处理中
        if (this.processingTasks.has(task.id)) {
          logger.warn(`任务 ${task.id} 已在处理中，跳过`, { taskId: task.id })
          continue
        }

        // 异步处理单个任务
        this.processSingleTaskAsync(task).catch(error => {
          logger.error(`异步任务处理失败`, error as Error, { taskId: task.id })
        })

        startedTasks++
      }

      logger.info(`✅ 已启动 ${startedTasks} 个任务的异步处理`, {
        action: 'async_batch_task_started',
        startedTasks,
        totalTasks: pendingTasks.length
      })

      return {
        success: true,
        message: `已启动 ${startedTasks} 个任务的处理`,
        startedTasks
      }

    } catch (error) {
      logger.error('异步批量任务处理启动失败', error as Error, { action: 'async_batch_task_error' })
      return {
        success: false,
        message: '启动任务处理失败',
        startedTasks: 0
      }
    }
  }

  /**
   * 异步处理单个任务
   */
  private async processSingleTaskAsync(task: Task): Promise<void> {
    const taskId = task.id

    try {
      // 使用乐观锁尝试获取任务处理权
      const lockResult = await this.acquireTaskLock(taskId)
      if (!lockResult.success) {
        logger.warn(`无法获取任务 ${taskId} 的处理锁: ${lockResult.error}`, { taskId })
        return
      }

      // 添加到处理中任务集合
      this.processingTasks.add(taskId)

      // 通知SSE监听器任务开始处理
      this.notifyTaskStatusChange(taskId, {
        status: 'PROCESSING_STARTED',
        message: '开始处理任务',
        timestamp: new Date().toISOString()
      })

      taskLog.start(taskId, task.title, task.mailId || undefined)

      // 处理任务
      const result = await this.processSingleTask(task)

      // 通知处理结果
      this.notifyTaskStatusChange(taskId, {
        status: result.success ? 'PROCESSING_COMPLETED' : 'PROCESSING_FAILED',
        message: result.success ? '任务处理完成' : (result.error || '处理失败'),
        timestamp: new Date().toISOString(),
        result: result.success ? {
          assignedProject: result.assignedProject,
          assignedPersonnel: result.assignedPersonnel
        } : undefined
      })

      if (result.success) {
        taskLog.success(taskId, task.title, {
          mailId: task.mailId,
          assignedProject: result.assignedProject,
          assignedPersonnel: result.assignedPersonnel
        })
      } else {
        taskLog.error(taskId, task.title, result.error || '未知错误', task.mailId || undefined)
      }

    } catch (error) {
      taskLog.error(taskId, task.title, error as Error, task.mailId || undefined)

      // 通知处理错误
      this.notifyTaskStatusChange(taskId, {
        status: 'PROCESSING_ERROR',
        message: error instanceof Error ? error.message : '未知错误',
        timestamp: new Date().toISOString()
      })
    } finally {
      // 从处理中任务集合移除
      this.processingTasks.delete(taskId)

      // 释放任务锁
      await this.releaseTaskLock(taskId).catch(error => {
        logger.error(`释放任务 ${taskId} 锁失败`, error as Error, { taskId })
      })
    }
  }

  /**
   * 处理单个任务
   */
  private async processSingleTask(task: Task): Promise<{
    success: boolean
    error?: string
    assignedProject?: string
    assignedPersonnel?: {
      processor?: string
      dataEntry?: string
      qualityControl?: string
    }
  }> {
    try {
      // 1. 获取任务创建人信息
      taskLog.step(task.id, '获取任务创建人信息', { mailId: task.mailId })
      const creator = await prisma.user.findUnique({
        where: { id: task.createdById },
        select: {
          domainAccount: true,
          password: true,
          email: true,
          name: true
        }
      })

      if (!creator || !creator.domainAccount || !creator.password) {
        throw new Error('任务创建人信息不完整或缺少域账号/密码')
      }
  
      // 构建基本的metadata信息
      const metadata = await emlFileService.parseEmlFile(task.emlFile || ''); 

      // 新的TaskAttachment表方式
      if (!metadata.attachments || metadata.attachments.length < 1) {
        throw new Error('邮件中没有附件')
      }

      // 从文件系统读取附件内容
      const attachments =  metadata.attachments || []

      // 2. 检查邮件附件
      taskLog.step(task.id, '检查邮件附件', {
        mailId: task.mailId,
        attachmentCount: attachments.length
      })
      if(attachments.length < 1){
        throw new Error('邮件中没有可用的附件内容')
      }

      let attachment = attachments?.find((a: any) => a.name.endsWith('.pdf'))

      // 3. AI识别主要附件
      taskLog.step(task.id, 'AI识别主要附件', {
        mailId: task.mailId,
        attachmentNames: attachments.map((a: any) => a.name)
      })
      const aiFindAttachment = await this.aiExtractor.findMainAttachment(
        metadata.subject,
        metadata.body,
        metadata.from,
        attachments.map((a: any) => a.name)
      )
      if(!aiFindAttachment.saeAttachmentName){
        throw new Error('AI未能自动识别邮件中的附件')
      }
      attachment = attachments.find((a: any) => a.name === aiFindAttachment.saeAttachmentName)
      if(!attachment){
        throw new Error('AI自动识别邮件附件名称异常')
      }

      // 4. 处理PDF文件
      taskLog.step(task.id, '处理PDF文件', {
        mailId: task.mailId,
        fileName: attachment.name
      })
      const pdf = await PDFProcessorService.processPdfWithOCR(Buffer.from(attachment.content, 'base64'), attachment.name)
      if(!pdf.isPageNumberContinuity){
        throw new Error('pdf页码不完整')
      }
      if(!pdf.hasSignature){
        throw new Error('pdf未签名')
      }
      // if(!pdf.isPdfComplete){
      //   throw new Error('pdf内容不完整')
      // }

      // 5. 提取SAE信息
      taskLog.step(task.id, '提取SAE信息', { mailId: task.mailId })
      const extractedText = pdf.extractedText || '';
      const saeInfo = await this.aiExtractor.extractSAEInfo(extractedText)

      // 6. 匹配项目
      taskLog.step(task.id, '匹配项目', {
        mailId: task.mailId,
        protocolNumber: saeInfo.data.protocolNumber
      })
      const matchedProject = await projectService.matchProject(saeInfo.data.protocolNumber);
      if(!matchedProject){
        throw new Error(`${saeInfo.data.protocolNumber} -- 项目匹配失败`)
      }

      logger.info('项目匹配成功', {
        taskId: task.id,
        projectId: matchedProject.id,
        projectCode: matchedProject.projectCode,
        projectName: matchedProject.projectName
      });

      // 7. 人员分配
      taskLog.step(task.id, '分配处理人员', { mailId: task.mailId })
      const assignedPersonnel = await personnelAssignmentService.assignPersonnel(matchedProject?.id)

      // 8. 更新任务信息
      taskLog.step(task.id, '更新任务状态', { mailId: task.mailId })
      const updateData: any = {
        updatedAt: new Date(),
        version: {
          increment: 1
        }
      }

      // 分配项目
      updateData.projectId = matchedProject.id

      // 分配人员
      if (assignedPersonnel.processor) {
        updateData.assignedToId = assignedPersonnel.processor.id
      }
      if (assignedPersonnel.dataEntry) {
        updateData.deToId = assignedPersonnel.dataEntry.id
      }
      if (assignedPersonnel.qualityControl) {
        updateData.qcToId = assignedPersonnel.qualityControl.id
      }

      await prisma.task.update({
        where: { id: task.id },
        data: updateData
      })

      // 9. 创建SAE报告
      taskLog.step(task.id, '创建SAE报告', { mailId: task.mailId })
      const saeReport = await saeReportService.createSAEReport({
        saeInfo: saeInfo.data,
        projectId: matchedProject.id,
        originalEmailId: task.mailId || ''
      })

      // 10. 生成邮件正文PDF
      taskLog.step(task.id, '生成邮件正文PDF', { mailId: task.mailId })
      const emailBodyPDF = await fileAttachmentService.htmlToPDFBuffer(metadata.body)

      // 11. 合并所有附件到一个PDF
      taskLog.step(task.id, '合并PDF附件', {
        mailId: task.mailId,
        attachmentCount: attachments.length
      })
      const mergedPDF = await fileAttachmentService.mergeAttachmentsToPDF(
        emailBodyPDF,
        attachments.map((a: any) => ({
          name: a.name,
          contentType: a.contentType,
          size: a.size,
          content: a.content,
          isInline: a.isInline || false
        })),
        saeReport.id
      )

      // 12. 保存合并后的PDF到附件表
      taskLog.step(task.id, '保存合并PDF文件', { mailId: task.mailId })
      await fileAttachmentService.saveAttachment({
        reportId: saeReport.id,
        fileName: mergedPDF.fileName,
        filePath: mergedPDF.filePath,
        type: ReportAttachmentType.SAE_PDF,
        fileSize: mergedPDF.fileSize,
        fileType: 'application/pdf',
        mimeType: 'application/pdf'
      })

      // 13. 准备邮件模板变量
      taskLog.step(task.id, '准备邮件模板变量', { mailId: task.mailId })
      const templateVariables = {
        protocolNumber: saeInfo.data.protocolNumber,
        subjectNumber: saeInfo.data.subjectNumber,
        eventName: saeInfo.data.eventName,
        seriousness: saeInfo.data.seriousness,
        eventType: saeInfo.data.eventType,
        causality: saeInfo.data.causality,
        learnedDate: saeInfo.data.learnedDate,
        centerName: saeInfo.data.centerName,
        projectCode: matchedProject.projectCode,
        projectName: matchedProject.projectName,
        sponsor: matchedProject.sponsor || undefined,
        pvEmail: matchedProject.pvEmail || undefined,
        processorName: assignedPersonnel.processor?.name,
        processorEmail: assignedPersonnel.processor?.email,
        dataEntryName: assignedPersonnel.dataEntry?.name,
        dataEntryEmail: assignedPersonnel.dataEntry?.email,
        qualityControlName: assignedPersonnel.qualityControl?.name,
        qualityControlEmail: assignedPersonnel.qualityControl?.email,
        reportUuid: saeReport.reportUuid
      }

      // 14. 生成所有类型的邮件
      taskLog.step(task.id, '生成邮件模板', { mailId: task.mailId })
      const generatedEmails = await emailTemplateService.generateAllEmailTypes(
        matchedProject.id,
        templateVariables
      )

      // 15. 创建EML文件
      taskLog.step(task.id, '创建EML文件', { mailId: task.mailId })
      const emlFiles = await emailTemplateService.createAllEMLFiles(
        generatedEmails,
        saeReport.id
      )

      // 16. 保存EML文件到附件表
      taskLog.step(task.id, '保存EML文件', {
        mailId: task.mailId,
        emlFileCount: emlFiles.size
      })
      for (const [_templateType, emlFile] of emlFiles) {
        await fileAttachmentService.saveAttachment({
          reportId: saeReport.id,
          fileName: emlFile.fileName,
          filePath: emlFile.filePath,
          type: ReportAttachmentType.SUB_MAIL_EML,
          fileSize: emlFile.fileSize,
          fileType: 'message/rfc822',
          mimeType: 'message/rfc822'
        })
      }

      // 17. 发送基于角色的通知邮件
      taskLog.step(task.id, '发送通知邮件', { mailId: task.mailId })
      const notificationResult = await notificationService.sendRoleBasedNotifications(
        saeReport.id,
        matchedProject.id,
        {
          processor: assignedPersonnel.processor ? {
            id: assignedPersonnel.processor.id,
            name: assignedPersonnel.processor.name,
            email: assignedPersonnel.processor.email,
            roles: assignedPersonnel.processor.roles as UserRole[]
          } : undefined,
          dataEntry: assignedPersonnel.dataEntry ? {
            id: assignedPersonnel.dataEntry.id,
            name: assignedPersonnel.dataEntry.name,
            email: assignedPersonnel.dataEntry.email,
            roles: assignedPersonnel.dataEntry.roles as UserRole[]
          } : undefined,
          qualityControl: assignedPersonnel.qualityControl ? {
            id: assignedPersonnel.qualityControl.id,
            name: assignedPersonnel.qualityControl.name,
            email: assignedPersonnel.qualityControl.email,
            roles: assignedPersonnel.qualityControl.roles as UserRole[]
          } : undefined
        },
        emlFiles,
        {
          domainAccount: creator.domainAccount,
          password: creator.password
        }
      )

      logger.info('通知邮件发送完成', {
        taskId: task.id,
        mailId: task.mailId,
        notificationResult
      })

      // 18. 更新SAE报告状态
      taskLog.step(task.id, '更新SAE报告状态', { mailId: task.mailId })
      await saeReportService.updateReportStatus(saeReport.id, ReportStatus.PROCESSING)

      // 19. 完成任务
      taskLog.step(task.id, '完成任务处理', { mailId: task.mailId })
      await prisma.task.update({
        where: { id: task.id },
        data: {
          status: TaskStatus.COMPLETED,
          completedAt: new Date(),
          processingStartedAt: null,
          updatedAt: new Date(),
          version: {
            increment: 1
          }
        }
      })

      return {
        success: true,
        assignedProject: matchedProject?.projectCode,
        assignedPersonnel: {
          processor: assignedPersonnel.processor?.name,
          dataEntry: assignedPersonnel.dataEntry?.name,
          qualityControl: assignedPersonnel.qualityControl?.name
        }
      }

    } catch (error) {
      taskLog.error(task.id, task.title, error as Error, task.mailId || undefined)

      // 更新任务状态为失败
      await prisma.task.update({
        where: { id: task.id },
        data: {
          status: TaskStatus.PENDING, // 保持待处理状态，便于重试
          lastError: error instanceof Error ? error.message : '未知错误',
          lastErrorTime: new Date().toISOString(),
          processingStartedAt: null,
          version: {
            increment: 1
          }
        }
      }).catch((updateError) => {
        logger.error('更新任务失败状态时出错', updateError as Error, {
          taskId: task.id,
          mailId: task.mailId
        })
      })

      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误'
      }
    }
  }

  /**
   * 获取待处理任务
   */
  private async getPendingTasks() {
    logger.debug('查询待处理任务', { action: 'query_pending_tasks' })
    return await prisma.task.findMany({
      where: {
        status: {
          in: [TaskStatus.PENDING]
        }
      },
      include: {
        createdBy: true
      },
      orderBy: {
        createdAt: 'asc'
      },
      take: 10 // 限制每次处理的任务数量
    })
  }
  
  /**
   * 获取处理统计信息
   */
  async getProcessingStats(): Promise<ProcessingStats> {
    const stats = await prisma.task.groupBy({
      by: ['status'],
      _count: {
        status: true
      }
    })

    const result: ProcessingStats = {
      totalTasks: 0,
      pendingTasks: 0,
      processingTasks: 0,
      completedTasks: 0,
      failedTasks: 0
    }

    stats.forEach((stat: any) => {
      result.totalTasks += stat._count.status

      switch (stat.status) {
        case TaskStatus.PENDING:
          result.pendingTasks = stat._count.status
          break
        case TaskStatus.IN_PROGRESS:
          result.processingTasks = stat._count.status
          break
        case TaskStatus.COMPLETED:
          result.completedTasks = stat._count.status
          break
        case TaskStatus.CANCELLED:
          result.failedTasks = stat._count.status
          break
      }
    })

    return result
  }

  /**
   * 获取任务处理锁（乐观锁）
   */
  private async acquireTaskLock(taskId: number): Promise<{ success: boolean; error?: string }> {
    try {
      // 使用乐观锁更新任务状态
      const result = await prisma.task.updateMany({
        where: {
          id: taskId,
          status: 'PENDING', // 只有待处理状态的任务才能被获取
          processingStartedAt: null // 确保没有其他进程在处理
        },
        data: {
          status: 'IN_PROGRESS',
          processingStartedAt: new Date(),
          version: {
            increment: 1
          }
        }
      })

      if (result.count === 0) {
        return {
          success: false,
          error: '任务已被其他进程处理或状态不正确'
        }
      }

      return { success: true }
    } catch (error) {
      logger.error(`获取任务 ${taskId} 锁失败`, error as Error, { taskId })
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误'
      }
    }
  }

  /**
   * 释放任务处理锁
   */
  private async releaseTaskLock(taskId: number): Promise<void> {
    try {
      await prisma.task.update({
        where: { id: taskId },
        data: {
          processingStartedAt: null,
          version: {
            increment: 1
          }
        }
      })
    } catch (error) {
      logger.error(`释放任务 ${taskId} 锁失败`, error as Error, { taskId })
      throw error
    }
  }

  /**
   * 通知任务状态变更（SSE）
   */
  private notifyTaskStatusChange(taskId: number, data: any): void {
    const message = JSON.stringify({
      type: 'TASK_STATUS_UPDATE',
      taskId,
      data
    })

    // 通知所有监听器
    this.taskStatusListeners.forEach((listener) => {
      try {
        listener(message)
      } catch (error) {
        logger.error('SSE通知发送失败', error as Error, { taskId })
      }
    })
  }

  /**
   * 添加SSE监听器
   */
  addStatusListener(listenerId: string, listener: (data: any) => void): void {
    this.taskStatusListeners.set(listenerId, listener)
    logger.debug(`添加SSE监听器: ${listenerId}`)
  }

  /**
   * 移除SSE监听器
   */
  removeStatusListener(listenerId: string): void {
    this.taskStatusListeners.delete(listenerId)
    logger.debug(`移除SSE监听器: ${listenerId}`)
  }

  /**
   * 获取当前处理中的任务数量
   */
  getProcessingTaskCount(): number {
    return this.processingTasks.size
  }

  /**
   * 获取处理中的任务ID列表
   */
  getProcessingTaskIds(): number[] {
    return Array.from(this.processingTasks)
  }

  /**
   * 检查特定任务是否正在处理
   */
  isTaskProcessing(taskId: number): boolean {
    return this.processingTasks.has(taskId)
  }

}

// 导出单例实例
export const taskProcessorService = new TaskProcessorService()
