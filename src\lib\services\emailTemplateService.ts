import { prisma } from '@/lib/db'
import { promises as fs } from 'fs'
import path from 'path'
import { EmailTemplateType } from 'generated-prisma'
import { uploadDir } from '@/config'
import type { ExtractedSAEInfo } from './aiExtractor'

export interface EmailTemplateVariables {
  // SAE信息相关
  protocolNumber?: string
  subjectNumber?: string
  eventName?: string
  seriousness?: string
  eventType?: string
  causality?: string
  learnedDate?: string
  centerName?: string
  
  // 项目信息相关
  projectCode?: string
  projectName?: string
  sponsor?: string
  pvEmail?: string
  
  // 人员信息相关
  processorName?: string
  processorEmail?: string
  dataEntryName?: string
  dataEntryEmail?: string
  qualityControlName?: string
  qualityControlEmail?: string
  
  // 系统信息相关
  reportUuid?: string
  currentDate?: string
  currentTime?: string
  
  // 自定义变量
  [key: string]: any
}

export interface GeneratedEmail {
  subject: string
  body: string
  recipients: string[]
  ccRecipients: string[]
  attachments?: string[]
}

export interface EMLFileResult {
  filePath: string
  fileName: string
  fileSize: number
}

/**
 * 邮件模板服务
 * 负责邮件模板的处理、变量替换和EML文件生成
 */
export class EmailTemplateService {
  private readonly emlDir = path.join(uploadDir, 'eml-files')
  
  constructor() {
    this.ensureEMLDir()
  }
  
  /**
   * 确保EML目录存在
   */
  private async ensureEMLDir(): Promise<void> {
    try {
      await fs.mkdir(this.emlDir, { recursive: true })
    } catch (error) {
      console.error('创建EML目录失败:', error)
    }
  }
  
  /**
   * 根据项目和类型获取邮件模板
   */
  async getTemplatesByProject(projectId: number, templateType?: EmailTemplateType) {
    try {
      const where: any = {
        OR: [
          { projectId: projectId },
          { projectId: null, isDefault: true }
        ]
      }
      
      if (templateType) {
        where.templateType = templateType
      }
      
      const templates = await prisma.emailTemplate.findMany({
        where,
        orderBy: [
          { projectId: 'desc' }, // 项目特定模板优先
          { isDefault: 'desc' },
          { createdAt: 'desc' }
        ]
      })
      
      // 去重，项目特定模板优先
      const uniqueTemplates = new Map()
      templates.forEach(template => {
        const key = template.templateType
        if (!uniqueTemplates.has(key) || template.projectId === projectId) {
          uniqueTemplates.set(key, template)
        }
      })
      
      return Array.from(uniqueTemplates.values())
      
    } catch (error) {
      console.error('获取邮件模板失败:', error)
      throw new Error(`获取邮件模板失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }
  
  /**
   * 根据模板生成邮件内容
   */
  async generateEmailFromTemplate(
    templateId: number,
    variables: EmailTemplateVariables
  ): Promise<GeneratedEmail> {
    try {
      const template = await prisma.emailTemplate.findUnique({
        where: { id: templateId }
      })
      
      if (!template) {
        throw new Error(`邮件模板不存在: ${templateId}`)
      }
      
      // 替换变量
      const subject = this.replaceVariables(template.subjectTemplate || '', variables)
      const body = this.replaceVariables(template.bodyTemplate, variables)
      
      // 处理收件人和抄送人
      const recipients = this.processEmailList(template.recipientEmails as string[] || [], variables)
      const ccRecipients = this.processEmailList(template.ccEmails as string[] || [], variables)
      
      return {
        subject,
        body,
        recipients,
        ccRecipients
      }
      
    } catch (error) {
      console.error('生成邮件内容失败:', error)
      throw new Error(`生成邮件内容失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }
  
  /**
   * 生成所有类型的邮件
   */
  async generateAllEmailTypes(
    projectId: number,
    variables: EmailTemplateVariables
  ): Promise<Map<EmailTemplateType, GeneratedEmail>> {
    try {
      const templates = await this.getTemplatesByProject(projectId)
      const generatedEmails = new Map<EmailTemplateType, GeneratedEmail>()
      
      for (const template of templates) {
        try {
          const email = await this.generateEmailFromTemplate(template.id, variables)
          generatedEmails.set(template.templateType, email)
          console.log(`生成邮件成功: ${template.templateType}`)
        } catch (error) {
          console.warn(`生成邮件失败: ${template.templateType}`, error)
        }
      }
      
      return generatedEmails
      
    } catch (error) {
      console.error('批量生成邮件失败:', error)
      throw new Error(`批量生成邮件失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }
  
  /**
   * 创建EML文件
   */
  async createEMLFile(
    email: GeneratedEmail,
    templateType: EmailTemplateType,
    reportId: number
  ): Promise<EMLFileResult> {
    try {
      const fileName = `${templateType.toLowerCase()}_${reportId}_${Date.now()}.eml`
      const filePath = path.join(this.emlDir, fileName)
      
      // 构建EML内容
      const emlContent = this.buildEMLContent(email)
      
      // 写入文件
      await fs.writeFile(filePath, emlContent, 'utf8')
      
      // 获取文件大小
      const stats = await fs.stat(filePath)
      
      const result: EMLFileResult = {
        filePath,
        fileName,
        fileSize: stats.size
      }
      
      console.log('EML文件创建成功:', result)
      return result
      
    } catch (error) {
      console.error('创建EML文件失败:', error)
      throw new Error(`创建EML文件失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }
  
  /**
   * 批量创建EML文件
   */
  async createAllEMLFiles(
    emails: Map<EmailTemplateType, GeneratedEmail>,
    reportId: number
  ): Promise<Map<EmailTemplateType, EMLFileResult>> {
    const emlFiles = new Map<EmailTemplateType, EMLFileResult>()
    
    for (const [templateType, email] of emails) {
      try {
        const emlFile = await this.createEMLFile(email, templateType, reportId)
        emlFiles.set(templateType, emlFile)
      } catch (error) {
        console.warn(`创建EML文件失败: ${templateType}`, error)
      }
    }
    
    return emlFiles
  }
  
  /**
   * 替换模板变量
   */
  private replaceVariables(template: string, variables: EmailTemplateVariables): string {
    let result = template
    
    // 添加系统默认变量
    const allVariables = {
      ...variables,
      currentDate: new Date().toLocaleDateString('zh-CN'),
      currentTime: new Date().toLocaleString('zh-CN')
    }
    
    // 替换 {{variable}} 格式的变量
    for (const [key, value] of Object.entries(allVariables)) {
      if (value !== undefined && value !== null) {
        const regex = new RegExp(`\\{\\{\\s*${key}\\s*\\}\\}`, 'g')
        result = result.replace(regex, String(value))
      }
    }
    
    // 清理未替换的变量
    result = result.replace(/\{\{\s*\w+\s*\}\}/g, '')
    
    return result
  }
  
  /**
   * 处理邮件地址列表
   */
  private processEmailList(emails: string[], variables: EmailTemplateVariables): string[] {
    const result: string[] = []
    
    for (const email of emails) {
      if (email.includes('{{')) {
        // 包含变量的邮件地址
        const processedEmail = this.replaceVariables(email, variables)
        if (processedEmail && !processedEmail.includes('{{')) {
          result.push(processedEmail)
        }
      } else {
        // 静态邮件地址
        result.push(email)
      }
    }
    
    return result.filter(email => this.isValidEmail(email))
  }
  
  /**
   * 验证邮件地址格式
   */
  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  }
  
  /**
   * 构建EML文件内容
   */
  private buildEMLContent(email: GeneratedEmail): string {
    const now = new Date()
    const messageId = `<${Date.now()}.${Math.random().toString(36).substr(2, 9)}@triage.system>`
    
    let emlContent = ''
    
    // 邮件头部
    emlContent += `Message-ID: ${messageId}\r\n`
    emlContent += `Date: ${now.toUTCString()}\r\n`
    emlContent += `From: SAE Triage System <<EMAIL>>\r\n`
    emlContent += `To: ${email.recipients.join(', ')}\r\n`
    
    if (email.ccRecipients.length > 0) {
      emlContent += `Cc: ${email.ccRecipients.join(', ')}\r\n`
    }
    
    emlContent += `Subject: ${email.subject}\r\n`
    emlContent += `MIME-Version: 1.0\r\n`
    emlContent += `Content-Type: text/plain; charset=UTF-8\r\n`
    emlContent += `Content-Transfer-Encoding: 8bit\r\n`
    emlContent += `\r\n`
    
    // 邮件正文
    emlContent += email.body
    emlContent += `\r\n`
    
    return emlContent
  }
}

// 导出单例实例
export const emailTemplateService = new EmailTemplateService()
