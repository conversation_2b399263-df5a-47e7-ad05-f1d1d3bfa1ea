/* eslint-disable @typescript-eslint/no-require-imports */
const { createServer: createHttpsServer } = require('https');
const { createServer: createHttpServer } = require('http');
const { parse } = require('url');
const next = require('next');
const fs = require('fs');
const path = require('path');


// 环境配置
const dev = process.env.NODE_ENV !== 'production';
const port = process.env.PORT || 3100;
const app = next({ dev });
const handle = app.getRequestHandler();

// HTTPS 证书配置（仅开发环境）
let httpsOptions = null;
if (dev) {  
  process.env['NODE_TLS_REJECT_UNAUTHORIZED'] = 0;
  try {
    const certDir = path.join(require('os').homedir(), '.office-addin-dev-certs');
    httpsOptions = {
      key: fs.readFileSync(path.join(certDir, 'localhost.key')),
      cert: fs.readFileSync(path.join(certDir, 'localhost.crt')),
    };
  } catch (error) {
    console.warn('HTTPS 证书未找到，开发环境将使用 HTTP');
    console.warn('运行 "npm run outlook-cert" 安装 HTTPS 证书');
  }
}

// 定时任务配置
const setupCronJobs = () => {  
  fetch(`${dev? 'https' : 'http'}://localhost:${port}/api/scheduler`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ source: 'cron' })
  }); 
};

// 启动服务器
app.prepare().then(() => {
  const requestHandler = (req, res) => {
    const parsedUrl = parse(req.url, true);
    handle(req, res, parsedUrl);
  };

  let server;

  if (dev && httpsOptions) {
    // 开发环境使用 HTTPS
    server = createHttpsServer(httpsOptions, requestHandler);
    server.listen(port, (err) => {
      if (err) throw err;
      console.log(`> 开发服务器已启动: https://localhost:${port}`);
      setupCronJobs();
    });
  } else {
    // 生产环境或开发环境无证书时使用 HTTP
    server = createHttpServer(requestHandler);
    server.listen(port, (err) => {
      if (err) throw err;
      console.log(`> ${dev ? '开发' : '生产'}服务器已启动: http://localhost:${port}`);
      setupCronJobs();
    });
  }

  // // 优雅关闭
  // process.on('SIGTERM', () => {
  //   console.log('收到 SIGTERM 信号，正在关闭服务器...');
  //   server.close(() => {
  //     console.log('服务器已关闭');
  //     process.exit(0);
  //   });
  // });

  // process.on('SIGINT', () => {
  //   console.log('收到 SIGINT 信号，正在关闭服务器...');
  //   server.close(() => {
  //     console.log('服务器已关闭');
  //     process.exit(0);
  //   });
  // });
});