import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';
import { Send, Mail, Users, Settings, CheckSquare } from 'lucide-react';
import { UserInfo, Project } from '../types';
import { EmailSelector } from './EmailSelector';
import { EmailList } from './EmailList';
import { ProjectList } from './ProjectList'; 
import type { FullEmailInfo } from '../stores/EmailStore';

interface RoleTabsProps {
  userInfo: UserInfo;
  projects: Project[];
  selectedEmails: FullEmailInfo[];
  selectedEmailsCount: number;
  onCheckEmails: () => void;
  onClearEmails: () => void;
  onRemoveEmail: (emailId: string) => void;
  onDistribute: () => Promise<void>;
  onProcessEmails: (projectId: string) => Promise<void>;
}

export function RoleTabs({
  userInfo,
  projects,
  selectedEmails,
  selectedEmailsCount,
  onCheckEmails,
  onClearEmails,
  onRemoveEmail,
  onDistribute,
  onProcessEmails
}: RoleTabsProps) {
  // 获取用户可用的角色（只处理WM和Triage）
  const hasWMRole = userInfo.roles.find(role => role === 'WM' );  

  return (
    <div className="space-y-4"> 
      <Card>
        <CardContent className="p-3">
          <div className="flex gap-2 flex-wrap">
            <Button
              variant={hasWMRole ? 'default' : 'outline'}
              size="sm" 
              className="flex items-center gap-2"
            >
              <Send className="h-4 w-4" />
              邮件分发
            </Button>
          </div>
        </CardContent>
      </Card>

     

      {/* 角色特定功能面板 */}      
      <div className="space-y-4">
        <EmailSelector
          selectedEmailsCount={selectedEmailsCount}
          onCheckEmails={onCheckEmails}
          onClearEmails={onClearEmails}
        />

        {/* 选中的邮件列表 */}
        <EmailList
          selectedEmails={selectedEmails}
          onRemoveEmail={onRemoveEmail}
          onDistribute={onDistribute}
        />

        {/* 项目列表 */}
        <ProjectList
          projects={projects}
        />
      </div>
  
    </div>
  );
}
