import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { getCurrentUser, hasPermission } from '@/lib/auth'
import { EmailTemplateType } from 'generated-prisma'
import { emailTemplateTypeConfigService } from '@/lib/services'

const createConfigSchema = z.object({
  templateType: z.enum(['CONFIRMATION', 'SAFETY_NOTIFICATION', 'INTERNAL_FORWARD', 'WM_FORWARD']),
  projectId: z.number().optional(),
  defaultRecipients: z.array(z.string().email('请输入有效的收件人邮箱')).optional(),
  defaultCcRecipients: z.array(z.string().email('请输入有效的抄送人邮箱')).optional(),
  description: z.string().optional()
})

const updateConfigSchema = createConfigSchema.partial().extend({
  id: z.number()
})

/**
 * 获取邮件模板类型配置列表
 */
export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser(request)
    if (!user || !hasPermission(user.roles, ['PM', 'Triage'])) {
      return NextResponse.json({
        success: false,
        error: 'Unauthorized'
      }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const projectIdParam = searchParams.get('projectId')

    let projectId: number | null | undefined = undefined
    if (projectIdParam) {
      if (projectIdParam === 'null') {
        projectId = null // 只查询全局配置
      } else {
        projectId = parseInt(projectIdParam)
      }
    }

    const configs = await emailTemplateTypeConfigService.getConfigs({ projectId })

    return NextResponse.json({
      success: true,
      data: { configs }
    })

  } catch (error) {
    console.error('Get template type configs failed:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '获取配置失败'
    }, { status: 500 })
  }
}

/**
 * 创建邮件模板类型配置
 */
export async function POST(request: NextRequest) {
  try {
    const user = await getCurrentUser(request)
    if (!user || !hasPermission(user.roles, ['PM'])) {
      return NextResponse.json({
        success: false,
        error: 'Unauthorized'
      }, { status: 401 })
    }

    const body = await request.json()
    const configData = createConfigSchema.parse(body)

    const newConfig = await emailTemplateTypeConfigService.createConfig({
      templateType: configData.templateType as EmailTemplateType,
      projectId: configData.projectId,
      defaultRecipients: configData.defaultRecipients,
      defaultCcRecipients: configData.defaultCcRecipients,
      description: configData.description
    })

    return NextResponse.json({
      success: true,
      data: { config: newConfig },
      message: '配置创建成功'
    })

  } catch (error) {
    console.error('Create template type config failed:', error)

    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        error: '请求参数无效',
        details: error.errors
      }, { status: 400 })
    }

    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '创建配置失败'
    }, { status: 500 })
  }
}

/**
 * 更新邮件模板类型配置
 */
export async function PUT(request: NextRequest) {
  try {
    const user = await getCurrentUser(request)
    if (!user || !hasPermission(user.roles, ['PM'])) {
      return NextResponse.json({
        success: false,
        error: 'Unauthorized'
      }, { status: 401 })
    }

    const body = await request.json()
    const { id, ...updateData } = updateConfigSchema.parse(body)

    const updatedConfig = await emailTemplateTypeConfigService.updateConfig(id, {
      templateType: updateData.templateType as EmailTemplateType,
      projectId: updateData.projectId,
      defaultRecipients: updateData.defaultRecipients,
      defaultCcRecipients: updateData.defaultCcRecipients,
      description: updateData.description
    })

    return NextResponse.json({
      success: true,
      data: { config: updatedConfig },
      message: '配置更新成功'
    })

  } catch (error) {
    console.error('Update template type config failed:', error)

    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        error: '请求参数无效',
        details: error.errors
      }, { status: 400 })
    }

    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '更新配置失败'
    }, { status: 500 })
  }
}

/**
 * 删除邮件模板类型配置
 */
export async function DELETE(request: NextRequest) {
  try {
    const user = await getCurrentUser(request)
    if (!user || !hasPermission(user.roles, ['PM'])) {
      return NextResponse.json({
        success: false,
        error: 'Unauthorized'
      }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const id = parseInt(searchParams.get('id') || '')

    if (!id) {
      return NextResponse.json({
        success: false,
        error: '请提供配置ID'
      }, { status: 400 })
    }

    await emailTemplateTypeConfigService.deleteConfig(id)

    return NextResponse.json({
      success: true,
      message: '配置删除成功'
    })

  } catch (error) {
    console.error('Delete template type config failed:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '删除配置失败'
    }, { status: 500 })
  }
}
