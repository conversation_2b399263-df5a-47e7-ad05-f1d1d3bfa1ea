import { NextRequest, NextResponse } from 'next/server'
import { taskProcessorService } from '@/lib/services/taskProcessorService'
import { requireAuth } from '@/lib/auth'
import { UserRole } from 'generated-prisma'
import logger, { apiLog } from '@/lib/logger'

/**
 * 手动触发任务处理
 */
export async function POST(request: NextRequest) {
  const startTime = Date.now()
  apiLog.request('POST', '/api/jobs/process-tasks')

  try {
    // 验证用户权限 - 只有WM和PM角色可以触发
    const authResult = await requireAuth(request, [UserRole.WM, UserRole.PM])
    if (!authResult.success) {
      apiLog.response('POST', '/api/jobs/process-tasks', authResult.status, Date.now() - startTime, {
        error: 'Authentication failed'
      })
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      )
    }

    logger.info('用户触发任务处理', {
      userId: authResult.user?.id,
      userName: authResult.user?.name,
      action: 'manual_task_trigger'
    })

    // 执行异步任务处理
    logger.info('开始执行异步任务处理', {
      userId: authResult.user?.id,
      action: 'async_task_processing_start'
    })

    const processingResult = await taskProcessorService.processAllPendingTasksAsync()

    logger.info('异步任务处理启动完成', {
      userId: authResult.user?.id,
      startedTasks: processingResult.startedTasks,
      success: processingResult.success,
      action: 'async_task_processing_started'
    })

    apiLog.response('POST', '/api/jobs/process-tasks', 200, Date.now() - startTime, {
      startedTasks: processingResult.startedTasks,
      success: processingResult.success
    })

    return NextResponse.json({
      success: processingResult.success,
      message: processingResult.message,
      data: {
        startedTasks: processingResult.startedTasks
      }
    })

  } catch (error) {
    apiLog.error('POST', '/api/jobs/process-tasks', error as Error, {
      duration: Date.now() - startTime
    })
    return NextResponse.json(
      {
        success: false,
        error: '任务处理失败',
        details: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    )
  }
}

/**
 * 获取任务处理状态和统计信息
 */
export async function GET(request: NextRequest) {
  const startTime = Date.now()
  apiLog.request('GET', '/api/jobs/process-tasks')

  try {
    // 验证用户权限
    const authResult = await requireAuth(request, [UserRole.WM, UserRole.PM, UserRole.Triage])
    if (!authResult.success) {
      apiLog.response('GET', '/api/jobs/process-tasks', authResult.status, Date.now() - startTime, {
        error: 'Authentication failed'
      })
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      )
    }

    logger.debug('获取任务处理状态', {
      userId: authResult.user?.id,
      action: 'get_task_status'
    })

    // 获取处理统计信息
    const processingStats = await taskProcessorService.getProcessingStats()

    const processingTaskCount = taskProcessorService.getProcessingTaskCount()
    const processingTaskIds = taskProcessorService.getProcessingTaskIds()

    apiLog.response('GET', '/api/jobs/process-tasks', 200, Date.now() - startTime, {
      totalTasks: processingStats.totalTasks,
      pendingTasks: processingStats.pendingTasks,
      processingTaskCount
    })

    return NextResponse.json({
      success: true,
      data: {
        processingStats,
        processingTaskCount,
        processingTaskIds
      }
    })

  } catch (error) {
    apiLog.error('GET', '/api/jobs/process-tasks', error as Error, {
      duration: Date.now() - startTime
    })
    return NextResponse.json(
      {
        success: false,
        error: '获取状态失败',
        details: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    )
  }
}
