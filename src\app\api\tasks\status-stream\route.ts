import { NextRequest } from 'next/server'
import { taskProcessorService } from '@/lib/services/taskProcessorService'
import { requireAuth } from '@/lib/auth'
import { UserRole } from 'generated-prisma'
import logger from '@/lib/logger'

/**
 * SSE接口 - 实时获取任务状态更新
 */
export async function GET(request: NextRequest) {
  try {
    // 验证用户权限
    const authResult = await requireAuth(request, [UserRole.WM, UserRole.PM, UserRole.Triage])
    if (!authResult.success) {
      return new Response('Unauthorized', { status: 401 })
    }

    logger.info('用户连接任务状态SSE流', {
      userId: authResult.user?.id,
      userName: authResult.user?.name,
      action: 'sse_connect'
    })

    // 创建SSE响应
    const encoder = new TextEncoder()
    const stream = new ReadableStream({
      start(controller) {
        // 生成唯一的监听器ID
        const listenerId = `${authResult.user?.id}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
        
        // 发送连接成功消息
        const connectMessage = `data: ${JSON.stringify({
          type: 'CONNECTED',
          message: '已连接到任务状态流',
          timestamp: new Date().toISOString()
        })}\n\n`
        controller.enqueue(encoder.encode(connectMessage))

        // 发送当前处理状态
        const currentStatus = {
          type: 'CURRENT_STATUS',
          data: {
            processingTaskCount: taskProcessorService.getProcessingTaskCount(),
            processingTaskIds: taskProcessorService.getProcessingTaskIds()
          },
          timestamp: new Date().toISOString()
        }
        const statusMessage = `data: ${JSON.stringify(currentStatus)}\n\n`
        controller.enqueue(encoder.encode(statusMessage))

        // 添加状态监听器
        const listener = (data: string) => {
          try {
            const message = `data: ${data}\n\n`
            controller.enqueue(encoder.encode(message))
          } catch (error) {
            logger.error('SSE消息发送失败', error as Error, { listenerId })
          }
        }

        taskProcessorService.addStatusListener(listenerId, listener)

        // 定期发送心跳
        const heartbeatInterval = setInterval(() => {
          try {
            const heartbeat = `data: ${JSON.stringify({
              type: 'HEARTBEAT',
              timestamp: new Date().toISOString()
            })}\n\n`
            controller.enqueue(encoder.encode(heartbeat))
          } catch (error) {
            logger.error('SSE心跳发送失败', error as Error, { listenerId })
            clearInterval(heartbeatInterval)
          }
        }, 30000) // 每30秒发送一次心跳

        // 清理函数
        const cleanup = () => {
          clearInterval(heartbeatInterval)
          taskProcessorService.removeStatusListener(listenerId)
          logger.info('用户断开任务状态SSE流', {
            userId: authResult.user?.id,
            listenerId,
            action: 'sse_disconnect'
          })
        }

        // 监听连接关闭
        request.signal.addEventListener('abort', cleanup)
        
        // 存储清理函数以便在错误时调用
        ;(controller as any).cleanup = cleanup
      },
      
      cancel() {
        // 连接被取消时的清理
        if ((this as any).cleanup) {
          (this as any).cleanup()
        }
      }
    })

    return new Response(stream, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Cache-Control'
      }
    })

  } catch (error) {
    logger.error('SSE连接建立失败', error as Error)
    return new Response('Internal Server Error', { status: 500 })
  }
}
