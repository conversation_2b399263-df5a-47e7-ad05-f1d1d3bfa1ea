import { POST } from './route'
import { NextRequest } from 'next/server'
import { prisma } from '@/lib/db'
import { getCurrentUser } from '@/lib/auth'
import { emlFileService } from '@/lib/services/emlFileService'

// Mock dependencies
jest.mock('@/lib/db', () => ({
  prisma: {
    task: {
      create: jest.fn()
    }
  }
}))

jest.mock('@/lib/auth', () => ({
  getCurrentUser: jest.fn()
}))

jest.mock('@/lib/services/emlFileService', () => ({
  emlFileService: {
    generateEmlFromEmailDetails: jest.fn(), 
  }
}))

describe('/api/outlook/distribute-emails', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  test('应该成功处理邮件分发请求', async () => {
    // Mock用户认证
    const mockUser = {
      id: 1,
      email: '<EMAIL>',
      roles: ['WM']
    };
    (getCurrentUser as jest.Mock).mockResolvedValue(mockUser)

    // Mock EML文件生成
    const mockEmlFileInfo = {
      filePath: 'test.eml', // 现在使用相对路径
      fileName: 'test.eml',
      fileSize: 1024,
      fileHash: 'abc123'
    };
    (emlFileService.generateEmlFromEmailDetails as jest.Mock).mockResolvedValue(mockEmlFileInfo)

    // Mock任务创建
    const mockTask = {
      id: 1,
      title: '测试邮件',
      description: '来自 <EMAIL> 的邮件',
      status: 'PENDING',
      priority: 'MEDIUM',
      createdById: 1,
      emlFile: mockEmlFileInfo.filePath,
      mailId: 'test-mail-id'
    };
    (prisma.task.create as jest.Mock).mockResolvedValue(mockTask);
 
    // 构建请求
    const requestBody = {
      emails: [
        {
          itemId: 'test-mail-id',
          sender: '<EMAIL>',
          receivedTime: '2025-01-01T00:00:00Z',
          subject: '测试邮件',
          from: '<EMAIL>',
          to: '<EMAIL>',
          cc: '',
          date: new Date().toUTCString(),
          body: '<html><body>测试邮件内容</body></html>',
          attachments: [
            {
              name: 'test.pdf',
              size: 1024,
              isInline: false,
              id: 'att1',
              contentType: 'application/pdf',
              content: 'base64content',
              encoding: 'base64'
            }
          ]
        }
      ]
    }

    const request = new NextRequest('http://localhost:3000/api/outlook/distribute-emails', {
      method: 'POST',
      body: JSON.stringify(requestBody),
      headers: {
        'Content-Type': 'application/json'
      }
    })

    // 执行请求
    const response = await POST(request)
    const result = await response.json()

    // 验证结果
    expect(response.status).toBe(200)
    expect(result.success).toBe(true)
    expect(result.totalCount).toBe(1)
    expect(result.results).toHaveLength(1)
    expect(result.results[0].status).toBe('success')
    expect(result.results[0].taskId).toBe(1)
    expect(result.results[0].emlFile).toBe('test.eml')

    // 验证调用
    expect(getCurrentUser).toHaveBeenCalledWith(request)
    expect(emlFileService.generateEmlFromEmailDetails).toHaveBeenCalledWith(
      expect.objectContaining({
        from: '<EMAIL>',
        to: '<EMAIL>',
        subject: '测试邮件',
        body: '<html><body>测试邮件内容</body></html>'
      }),
      'test-mail-id'
    )
    expect(prisma.task.create).toHaveBeenCalledWith({
      data: expect.objectContaining({
        title: '测试邮件',
        description: '来自 <EMAIL> 的邮件',
        status: 'PENDING',
        priority: 'MEDIUM',
        createdById: 1,
        emlFile: mockEmlFileInfo.filePath,
        mailId: 'test-mail-id'
      })
    }) 
  })

  test('应该处理非WM用户的权限错误', async () => {
    const mockUser = {
      id: 1,
      email: '<EMAIL>',
      roles: ['Triage']
    };
    (getCurrentUser as jest.Mock).mockResolvedValue(mockUser)

    const requestBody = {
      emails: [
        {
          itemId: 'test-mail-id',
          sender: '<EMAIL>',
          receivedTime: '2025-01-01T00:00:00Z',
          subject: '测试邮件'
        }
      ]
    }

    const request = new NextRequest('http://localhost:3000/api/outlook/distribute-emails', {
      method: 'POST',
      body: JSON.stringify(requestBody)
    })

    const response = await POST(request)
    const result = await response.json()

    expect(response.status).toBe(403)
    expect(result.error).toBe('只有WM用户可以使用分发功能')
  })

  test('应该处理邮件数据不完整的错误', async () => {
    const mockUser = {
      id: 1,
      email: '<EMAIL>',
      roles: ['WM']
    };
    (getCurrentUser as jest.Mock).mockResolvedValue(mockUser)

    const requestBody = {
      emails: [
        {
          itemId: 'test-mail-id',
          sender: '<EMAIL>',
          receivedTime: '2025-01-01T00:00:00Z',
          subject: '测试邮件'
          // 缺少from和body字段
        }
      ]
    }

    const request = new NextRequest('http://localhost:3000/api/outlook/distribute-emails', {
      method: 'POST',
      body: JSON.stringify(requestBody)
    })

    const response = await POST(request)
    const result = await response.json()

    expect(response.status).toBe(200)
    expect(result.success).toBe(true)
    expect(result.results[0].status).toBe('error')
    expect(result.results[0].error).toContain('邮件数据不完整')
  })
})
